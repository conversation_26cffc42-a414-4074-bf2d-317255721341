// Quick test script to verify the enhanced agent system is working
const { generateAgentResponse } = require('./src/lib/agents/enhancedAgentSystem')

async function testAgent() {
  console.log('🧪 Testando o sistema de agentes aprimorado...\n')
  
  const testInput = 'Cliente quer campanha para Black Friday com budget de R$ 50.000, público jovem 18-25 anos, canais Instagram e Facebook'
  
  try {
    console.log('📝 Input de teste:', testInput)
    console.log('\n⚡ Gerando resposta...\n')
    
    const response = await generateAgentResponse('atendimento', testInput)
    
    console.log('✅ Resposta gerada com sucesso!')
    console.log('\n📊 Métricas:')
    console.log(`   • Confiança: ${Math.round(response.metadata.confidence * 100)}%`)
    console.log(`   • Completude: ${Math.round(response.metadata.completeness * 100)}%`)
    console.log(`   • Qualidade: ${Math.round(response.metadata.qualityScore * 100)}%`)
    console.log(`   • Seções: ${response.response.sections.length}`)
    console.log(`   • Itens de ação: ${response.response.actionItems.length}`)
    console.log(`   • Tempo: ${response.metadata.processingTime}ms`)
    
    console.log('\n📄 Prévia da resposta:')
    console.log(response.formattedOutput.substring(0, 500) + '...')
    
    console.log('\n🎯 Itens de ação identificados:')
    response.response.actionItems.forEach((item, index) => {
      console.log(`   ${index + 1}. ${item}`)
    })
    
    console.log('\n✨ Teste concluído com sucesso!')
    
  } catch (error) {
    console.error('❌ Erro no teste:', error.message)
    console.error(error.stack)
  }
}

testAgent()
