/**
 * Context-Aware Response Templates System
 * 
 * This module provides dynamic response templates that adapt based on input analysis,
 * user context, and agent specialization to generate comprehensive, structured responses.
 */

import { InputAnalysis, ResponseSection } from './responseEngine'
import { AgentKnowledgeBase } from './knowledgeBase'

export interface TemplateContext {
  agentId: string
  analysis: InputAnalysis
  input: string
  userContext?: Record<string, any>
}

export interface DynamicTemplate {
  id: string
  name: string
  description: string
  applicableWhen: (context: TemplateContext) => boolean
  priority: number
  sections: TemplateSectionGenerator[]
}

export interface TemplateSectionGenerator {
  id: string
  title: string
  type: 'analysis' | 'recommendation' | 'example' | 'warning' | 'next-steps' | 'best-practices' | 'detailed-guide'
  priority: number
  required: boolean
  condition?: (context: TemplateContext) => boolean
  generator: (context: TemplateContext) => ResponseSection
}

export class ResponseTemplateEngine {
  private static instance: ResponseTemplateEngine
  private templates: Map<string, DynamicTemplate[]> = new Map()
  private knowledgeBase: AgentKnowledgeBase

  private constructor() {
    this.knowledgeBase = AgentKnowledgeBase.getInstance()
    this.initializeTemplates()
  }

  public static getInstance(): ResponseTemplateEngine {
    if (!ResponseTemplateEngine.instance) {
      ResponseTemplateEngine.instance = new ResponseTemplateEngine()
    }
    return ResponseTemplateEngine.instance
  }

  public generateResponse(context: TemplateContext): ResponseSection[] {
    const agentTemplates = this.templates.get(context.agentId) || []
    
    // Find applicable templates
    const applicableTemplates = agentTemplates
      .filter(template => template.applicableWhen(context))
      .sort((a, b) => b.priority - a.priority)

    // Use the highest priority applicable template
    const selectedTemplate = applicableTemplates[0]
    if (!selectedTemplate) {
      return this.generateFallbackResponse(context)
    }

    // Generate sections from template
    const sections: ResponseSection[] = []
    
    for (const sectionGen of selectedTemplate.sections) {
      // Check if section condition is met (if any)
      if (sectionGen.condition && !sectionGen.condition(context)) {
        continue
      }

      try {
        const section = sectionGen.generator(context)
        sections.push(section)
      } catch (error) {
        console.error(`Error generating section ${sectionGen.id}:`, error)
        // Continue with other sections
      }
    }

    // Sort sections by priority
    return sections.sort((a, b) => b.priority - a.priority)
  }

  private initializeTemplates(): void {
    // Atendimento Agent Templates
    this.templates.set('atendimento', [
      {
        id: 'comprehensive_briefing',
        name: 'Briefing Abrangente',
        description: 'Template completo para análise e estruturação de briefings',
        applicableWhen: (context) => 
          context.analysis.intent.includes('briefing') || 
          context.analysis.keywords.includes('briefing') ||
          context.analysis.intent === 'create_briefing',
        priority: 10,
        sections: [
          {
            id: 'executive_summary',
            title: '📋 Resumo Executivo',
            type: 'analysis',
            priority: 10,
            required: true,
            generator: (context) => this.generateExecutiveSummary(context)
          },
          {
            id: 'detailed_analysis',
            title: '🔍 Análise Detalhada do Briefing',
            type: 'analysis',
            priority: 9,
            required: true,
            generator: (context) => this.generateDetailedAnalysis(context)
          },
          {
            id: 'missing_information',
            title: '❓ Informações Necessárias',
            type: 'warning',
            priority: 8,
            required: false,
            condition: (context) => context.analysis.completeness < 0.7,
            generator: (context) => this.generateMissingInformation(context)
          },
          {
            id: 'strategic_recommendations',
            title: '💡 Recomendações Estratégicas',
            type: 'recommendation',
            priority: 8,
            required: true,
            generator: (context) => this.generateStrategicRecommendations(context)
          },
          {
            id: 'timeline_breakdown',
            title: '📅 Cronograma Detalhado',
            type: 'detailed-guide',
            priority: 7,
            required: true,
            generator: (context) => this.generateTimelineBreakdown(context)
          },
          {
            id: 'approval_process',
            title: '✅ Processo de Aprovação',
            type: 'detailed-guide',
            priority: 6,
            required: true,
            generator: (context) => this.generateApprovalProcess(context)
          },
          {
            id: 'risk_assessment',
            title: '⚠️ Análise de Riscos',
            type: 'warning',
            priority: 7,
            required: false,
            condition: (context) => context.analysis.urgency === 'high' || context.analysis.complexity === 'advanced',
            generator: (context) => this.generateRiskAssessment(context)
          },
          {
            id: 'best_practices',
            title: '🎯 Melhores Práticas',
            type: 'best-practices',
            priority: 5,
            required: false,
            generator: (context) => this.generateBestPractices(context)
          },
          {
            id: 'next_steps',
            title: '🚀 Próximos Passos',
            type: 'next-steps',
            priority: 9,
            required: true,
            generator: (context) => this.generateNextSteps(context)
          }
        ]
      },
      {
        id: 'client_communication',
        name: 'Comunicação com Cliente',
        description: 'Template para situações de comunicação e alinhamento com cliente',
        applicableWhen: (context) => 
          context.analysis.keywords.some(k => ['cliente', 'comunicação', 'alinhamento', 'reunião'].includes(k)),
        priority: 8,
        sections: [
          {
            id: 'communication_analysis',
            title: '📞 Análise da Situação',
            type: 'analysis',
            priority: 10,
            required: true,
            generator: (context) => this.generateCommunicationAnalysis(context)
          },
          {
            id: 'communication_strategy',
            title: '💬 Estratégia de Comunicação',
            type: 'recommendation',
            priority: 9,
            required: true,
            generator: (context) => this.generateCommunicationStrategy(context)
          },
          {
            id: 'meeting_agenda',
            title: '📋 Agenda de Reunião Sugerida',
            type: 'detailed-guide',
            priority: 7,
            required: false,
            condition: (context) => context.analysis.keywords.includes('reunião'),
            generator: (context) => this.generateMeetingAgenda(context)
          }
        ]
      }
    ])

    // Planejamento Agent Templates
    this.templates.set('planejamento', [
      {
        id: 'strategic_planning',
        name: 'Planejamento Estratégico',
        description: 'Template completo para planejamento de campanhas',
        applicableWhen: (context) => 
          context.analysis.intent.includes('plan') || 
          context.analysis.keywords.includes('planejamento') ||
          context.analysis.keywords.includes('estratégia'),
        priority: 10,
        sections: [
          {
            id: 'strategic_overview',
            title: '🎯 Visão Estratégica',
            type: 'analysis',
            priority: 10,
            required: true,
            generator: (context) => this.generateStrategicOverview(context)
          },
          {
            id: 'market_analysis',
            title: '📊 Análise de Mercado',
            type: 'analysis',
            priority: 9,
            required: true,
            generator: (context) => this.generateMarketAnalysis(context)
          },
          {
            id: 'campaign_phases',
            title: '📅 Fases da Campanha',
            type: 'detailed-guide',
            priority: 8,
            required: true,
            generator: (context) => this.generateCampaignPhases(context)
          },
          {
            id: 'budget_allocation',
            title: '💰 Distribuição de Budget',
            type: 'detailed-guide',
            priority: 8,
            required: true,
            generator: (context) => this.generateBudgetAllocation(context)
          },
          {
            id: 'kpi_framework',
            title: '📈 Framework de KPIs',
            type: 'detailed-guide',
            priority: 7,
            required: true,
            generator: (context) => this.generateKPIFramework(context)
          }
        ]
      }
    ])

    // Mídia Agent Templates
    this.templates.set('midia', [
      {
        id: 'performance_analysis',
        name: 'Análise de Performance',
        description: 'Template para análise detalhada de performance de campanhas',
        applicableWhen: (context) => 
          context.analysis.intent.includes('analyze') || 
          context.analysis.keywords.some(k => ['performance', 'resultado', 'análise', 'métricas'].includes(k)),
        priority: 10,
        sections: [
          {
            id: 'performance_overview',
            title: '📊 Visão Geral da Performance',
            type: 'analysis',
            priority: 10,
            required: true,
            generator: (context) => this.generatePerformanceOverview(context)
          },
          {
            id: 'detailed_metrics',
            title: '📈 Métricas Detalhadas',
            type: 'analysis',
            priority: 9,
            required: true,
            generator: (context) => this.generateDetailedMetrics(context)
          },
          {
            id: 'optimization_opportunities',
            title: '🎯 Oportunidades de Otimização',
            type: 'recommendation',
            priority: 8,
            required: true,
            generator: (context) => this.generateOptimizationOpportunities(context)
          },
          {
            id: 'competitive_insights',
            title: '🏆 Insights Competitivos',
            type: 'analysis',
            priority: 7,
            required: false,
            generator: (context) => this.generateCompetitiveInsights(context)
          }
        ]
      }
    ])
  }

  // Section generators implementation
  private generateExecutiveSummary(context: TemplateContext): ResponseSection {
    const { analysis, input } = context
    
    let content = `**Status:** Briefing analisado com ${Math.round(analysis.completeness * 100)}% de completude\n`
    content += `**Complexidade:** ${this.translateComplexity(analysis.complexity)}\n`
    content += `**Urgência:** ${this.translateUrgency(analysis.urgency)}\n`
    content += `**Intenção Principal:** ${this.translateIntent(analysis.intent)}\n\n`
    
    if (analysis.context.budget) {
      content += `**Budget Identificado:** R$ ${analysis.context.budget}\n`
    }
    if (analysis.context.timeline) {
      content += `**Prazo:** ${analysis.context.timeline}\n`
    }
    if (analysis.context.channels && analysis.context.channels.length > 0) {
      content += `**Canais Mencionados:** ${analysis.context.channels.join(', ')}\n`
    }

    return {
      title: '📋 Resumo Executivo',
      content,
      type: 'analysis',
      priority: 10,
      metadata: { confidence: analysis.completeness }
    }
  }

  private generateDetailedAnalysis(context: TemplateContext): ResponseSection {
    const { analysis, input } = context
    
    let content = '### Elementos Identificados\n\n'
    
    if (analysis.keywords.length > 0) {
      content += `**Palavras-chave principais:** ${analysis.keywords.slice(0, 5).join(', ')}\n\n`
    }
    
    if (analysis.entities.length > 0) {
      content += `**Entidades detectadas:** ${analysis.entities.join(', ')}\n\n`
    }
    
    content += '### Contexto do Projeto\n\n'
    
    if (Object.keys(analysis.context).length > 0) {
      for (const [key, value] of Object.entries(analysis.context)) {
        content += `• **${this.formatContextKey(key)}:** ${value}\n`
      }
    } else {
      content += '• Contexto adicional será coletado durante o processo de briefing\n'
    }
    
    content += '\n### Análise de Sentimento\n\n'
    content += `O tom geral da solicitação é **${this.translateSentiment(analysis.sentiment)}**, `
    
    switch (analysis.sentiment) {
      case 'positive':
        content += 'indicando confiança e expectativas positivas para o projeto.'
        break
      case 'negative':
        content += 'sugerindo possíveis preocupações ou urgência que devem ser endereçadas.'
        break
      default:
        content += 'mantendo um tom profissional e objetivo.'
    }

    return {
      title: '🔍 Análise Detalhada do Briefing',
      content,
      type: 'analysis',
      priority: 9
    }
  }

  private generateMissingInformation(context: TemplateContext): ResponseSection {
    const { analysis } = context
    const missing: string[] = []
    
    if (!analysis.context.budget) missing.push('Orçamento disponível')
    if (!analysis.context.timeline) missing.push('Cronograma desejado')
    if (!analysis.context.targetAge) missing.push('Público-alvo detalhado')
    if (!analysis.context.channels) missing.push('Canais preferenciais')
    
    let content = '### Informações Essenciais Faltantes\n\n'
    
    if (missing.length > 0) {
      missing.forEach(item => {
        content += `• ${item}\n`
      })
      
      content += '\n### Impacto da Falta de Informações\n\n'
      content += '• **Planejamento:** Pode resultar em cronograma impreciso\n'
      content += '• **Orçamento:** Dificulta estimativa de custos e ROI\n'
      content += '• **Estratégia:** Limita precisão das recomendações\n'
      content += '• **Execução:** Pode causar retrabalho e atrasos\n'
      
      content += '\n### Recomendação\n\n'
      content += 'Agendar reunião de alinhamento para coletar informações faltantes antes de prosseguir com o planejamento detalhado.'
    } else {
      content += 'Todas as informações essenciais foram fornecidas. Excelente briefing!'
    }

    return {
      title: '❓ Informações Necessárias',
      content,
      type: 'warning',
      priority: 8
    }
  }

  // Additional generator methods would continue here...
  // Due to length constraints, I'll implement the key ones and add placeholders for others

  private generateStrategicRecommendations(context: TemplateContext): ResponseSection {
    const { analysis } = context
    const knowledge = this.knowledgeBase.getAgentKnowledge(context.agentId)
    const insights = this.knowledgeBase.getInsights(context.agentId, analysis)
    
    let content = '### Recomendações Baseadas na Análise\n\n'
    
    // Add insights from knowledge base
    insights.forEach((insight, index) => {
      content += `${index + 1}. **${insight.priority.toUpperCase()}:** ${insight.insight}\n\n`
    })
    
    // Add general recommendations based on analysis
    if (analysis.urgency === 'high') {
      content += '• **Recursos Adicionais:** Considere alocar recursos extras para garantir qualidade\n'
      content += '• **Comunicação Intensiva:** Estabeleça check-ins diários com o cliente\n'
    }
    
    if (analysis.complexity === 'advanced') {
      content += '• **Especialistas:** Envolver especialistas desde o início do projeto\n'
      content += '• **Faseamento:** Dividir projeto em fases menores para melhor controle\n'
    }

    return {
      title: '💡 Recomendações Estratégicas',
      content,
      type: 'recommendation',
      priority: 8
    }
  }

  private generateNextSteps(context: TemplateContext): ResponseSection {
    const { analysis } = context
    
    let content = '### Ações Imediatas (24-48h)\n\n'
    content += '1. ✅ Validar briefing com cliente\n'
    content += '2. 📋 Coletar informações faltantes\n'
    content += '3. 👥 Definir equipe do projeto\n'
    
    if (analysis.urgency === 'high') {
      content += '4. 🚨 Estabelecer cronograma acelerado\n'
    }
    
    content += '\n### Próxima Semana\n\n'
    content += '• Desenvolver estratégia detalhada\n'
    content += '• Criar cronograma executivo\n'
    content += '• Definir marcos de aprovação\n'
    content += '• Iniciar produção de materiais\n'
    
    content += '\n### Acompanhamento\n\n'
    content += '• **Check-ins:** Semanais (ou diários se urgente)\n'
    content += '• **Relatórios:** Status reports quinzenais\n'
    content += '• **Revisões:** Marcos de aprovação definidos\n'

    return {
      title: '🚀 Próximos Passos',
      content,
      type: 'next-steps',
      priority: 9
    }
  }

  // Placeholder methods for other generators
  private generateTimelineBreakdown(context: TemplateContext): ResponseSection {
    return { title: '📅 Cronograma Detalhado', content: 'Cronograma detalhado...', type: 'detailed-guide', priority: 7 }
  }

  private generateApprovalProcess(context: TemplateContext): ResponseSection {
    return { title: '✅ Processo de Aprovação', content: 'Processo de aprovação...', type: 'detailed-guide', priority: 6 }
  }

  private generateRiskAssessment(context: TemplateContext): ResponseSection {
    return { title: '⚠️ Análise de Riscos', content: 'Análise de riscos...', type: 'warning', priority: 7 }
  }

  private generateBestPractices(context: TemplateContext): ResponseSection {
    return { title: '🎯 Melhores Práticas', content: 'Melhores práticas...', type: 'best-practices', priority: 5 }
  }

  private generateCommunicationAnalysis(context: TemplateContext): ResponseSection {
    return { title: '📞 Análise da Situação', content: 'Análise da comunicação...', type: 'analysis', priority: 10 }
  }

  private generateCommunicationStrategy(context: TemplateContext): ResponseSection {
    return { title: '💬 Estratégia de Comunicação', content: 'Estratégia de comunicação...', type: 'recommendation', priority: 9 }
  }

  private generateMeetingAgenda(context: TemplateContext): ResponseSection {
    return { title: '📋 Agenda de Reunião Sugerida', content: 'Agenda de reunião...', type: 'detailed-guide', priority: 7 }
  }

  private generateStrategicOverview(context: TemplateContext): ResponseSection {
    return { title: '🎯 Visão Estratégica', content: 'Visão estratégica...', type: 'analysis', priority: 10 }
  }

  private generateMarketAnalysis(context: TemplateContext): ResponseSection {
    return { title: '📊 Análise de Mercado', content: 'Análise de mercado...', type: 'analysis', priority: 9 }
  }

  private generateCampaignPhases(context: TemplateContext): ResponseSection {
    return { title: '📅 Fases da Campanha', content: 'Fases da campanha...', type: 'detailed-guide', priority: 8 }
  }

  private generateBudgetAllocation(context: TemplateContext): ResponseSection {
    return { title: '💰 Distribuição de Budget', content: 'Distribuição de budget...', type: 'detailed-guide', priority: 8 }
  }

  private generateKPIFramework(context: TemplateContext): ResponseSection {
    return { title: '📈 Framework de KPIs', content: 'Framework de KPIs...', type: 'detailed-guide', priority: 7 }
  }

  private generatePerformanceOverview(context: TemplateContext): ResponseSection {
    return { title: '📊 Visão Geral da Performance', content: 'Visão geral da performance...', type: 'analysis', priority: 10 }
  }

  private generateDetailedMetrics(context: TemplateContext): ResponseSection {
    return { title: '📈 Métricas Detalhadas', content: 'Métricas detalhadas...', type: 'analysis', priority: 9 }
  }

  private generateOptimizationOpportunities(context: TemplateContext): ResponseSection {
    return { title: '🎯 Oportunidades de Otimização', content: 'Oportunidades de otimização...', type: 'recommendation', priority: 8 }
  }

  private generateCompetitiveInsights(context: TemplateContext): ResponseSection {
    return { title: '🏆 Insights Competitivos', content: 'Insights competitivos...', type: 'analysis', priority: 7 }
  }

  private generateFallbackResponse(context: TemplateContext): ResponseSection[] {
    return [{
      title: '📋 Análise Geral',
      content: 'Análise geral do input fornecido...',
      type: 'analysis',
      priority: 5
    }]
  }

  // Utility methods
  private translateComplexity(complexity: string): string {
    const translations = {
      'basic': 'Básica',
      'intermediate': 'Intermediária',
      'advanced': 'Avançada'
    }
    return translations[complexity as keyof typeof translations] || complexity
  }

  private translateUrgency(urgency: string): string {
    const translations = {
      'low': 'Baixa',
      'medium': 'Média',
      'high': 'Alta'
    }
    return translations[urgency as keyof typeof translations] || urgency
  }

  private translateIntent(intent: string): string {
    const translations = {
      'create_briefing': 'Criação de Briefing',
      'analyze_performance': 'Análise de Performance',
      'plan_campaign': 'Planejamento de Campanha',
      'optimize': 'Otimização',
      'report': 'Relatório',
      'troubleshoot': 'Resolução de Problemas',
      'general_inquiry': 'Consulta Geral'
    }
    return translations[intent as keyof typeof translations] || intent
  }

  private translateSentiment(sentiment: string): string {
    const translations = {
      'positive': 'positivo',
      'neutral': 'neutro',
      'negative': 'negativo'
    }
    return translations[sentiment as keyof typeof translations] || sentiment
  }

  private formatContextKey(key: string): string {
    const translations = {
      'budget': 'Orçamento',
      'timeline': 'Cronograma',
      'targetAge': 'Faixa Etária',
      'channels': 'Canais'
    }
    return translations[key as keyof typeof translations] || key
  }
}
