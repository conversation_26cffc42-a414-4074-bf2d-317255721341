/**
 * Input Analysis and Intelligence Layer
 * 
 * This module provides advanced input analysis capabilities including
 * sentiment analysis, keyword extraction, intent recognition, and context understanding.
 */

import { InputAnalysis } from './responseEngine'

export interface AdvancedAnalysis extends InputAnalysis {
  namedEntities: NamedEntity[]
  topicClusters: TopicCluster[]
  linguisticFeatures: LinguisticFeatures
  businessContext: BusinessContext
  riskFactors: RiskFactor[]
  opportunityIndicators: OpportunityIndicator[]
}

export interface NamedEntity {
  text: string
  type: 'PERSON' | 'ORGANIZATION' | 'LOCATION' | 'DATE' | 'MONEY' | 'PERCENTAGE' | 'PRODUCT' | 'SERVICE'
  confidence: number
  startIndex: number
  endIndex: number
}

export interface TopicCluster {
  name: string
  keywords: string[]
  relevance: number
  category: 'marketing' | 'business' | 'technical' | 'creative' | 'financial'
}

export interface LinguisticFeatures {
  readabilityScore: number
  formalityLevel: 'informal' | 'neutral' | 'formal'
  technicalComplexity: number
  emotionalIntensity: number
  questionCount: number
  imperativeCount: number
  averageSentenceLength: number
}

export interface BusinessContext {
  industry: string[]
  businessStage: 'startup' | 'growth' | 'mature' | 'enterprise'
  marketingMaturity: 'beginner' | 'intermediate' | 'advanced'
  budgetRange: 'micro' | 'small' | 'medium' | 'large' | 'enterprise'
  timeframe: 'immediate' | 'short-term' | 'medium-term' | 'long-term'
}

export interface RiskFactor {
  type: 'timeline' | 'budget' | 'scope' | 'technical' | 'market' | 'communication'
  description: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  probability: number
  impact: number
  mitigation: string[]
}

export interface OpportunityIndicator {
  type: 'growth' | 'efficiency' | 'innovation' | 'market' | 'competitive'
  description: string
  potential: 'low' | 'medium' | 'high'
  effort: 'low' | 'medium' | 'high'
  timeframe: 'immediate' | 'short' | 'medium' | 'long'
}

export class IntelligenceLayer {
  private static instance: IntelligenceLayer
  
  // Portuguese marketing/business vocabulary
  private readonly marketingTerms = new Set([
    'campanha', 'marketing', 'publicidade', 'propaganda', 'branding', 'brand',
    'roi', 'ctr', 'cpm', 'cpc', 'impressões', 'cliques', 'conversões',
    'segmentação', 'targeting', 'remarketing', 'lookalike', 'funil',
    'awareness', 'consideração', 'conversão', 'retenção', 'advocacy'
  ])

  private readonly businessTerms = new Set([
    'cliente', 'negócio', 'empresa', 'startup', 'corporação', 'b2b', 'b2c',
    'receita', 'faturamento', 'lucro', 'margem', 'investimento', 'capital',
    'mercado', 'concorrência', 'competidor', 'nicho', 'segmento'
  ])

  private readonly technicalTerms = new Set([
    'api', 'crm', 'erp', 'analytics', 'dashboard', 'kpi', 'métrica',
    'automação', 'integração', 'plataforma', 'sistema', 'ferramenta',
    'pixel', 'tag', 'tracking', 'attribution', 'attribution modeling'
  ])

  private readonly urgencyIndicators = new Set([
    'urgente', 'imediato', 'hoje', 'agora', 'já', 'rápido', 'asap',
    'emergência', 'crítico', 'prioritário', 'ontem', 'deadline'
  ])

  private readonly positiveIndicators = new Set([
    'ótimo', 'excelente', 'perfeito', 'maravilhoso', 'fantástico',
    'sucesso', 'aprovado', 'satisfeito', 'feliz', 'positivo', 'bom'
  ])

  private readonly negativeIndicators = new Set([
    'problema', 'erro', 'falha', 'ruim', 'péssimo', 'insatisfeito',
    'preocupado', 'crítico', 'negativo', 'rejeitado', 'cancelado'
  ])

  private constructor() {}

  public static getInstance(): IntelligenceLayer {
    if (!IntelligenceLayer.instance) {
      IntelligenceLayer.instance = new IntelligenceLayer()
    }
    return IntelligenceLayer.instance
  }

  public performAdvancedAnalysis(input: string): AdvancedAnalysis {
    // Basic analysis first
    const basicAnalysis = this.performBasicAnalysis(input)
    
    // Advanced analysis
    const namedEntities = this.extractNamedEntities(input)
    const topicClusters = this.identifyTopicClusters(input)
    const linguisticFeatures = this.analyzeLinguisticFeatures(input)
    const businessContext = this.inferBusinessContext(input, namedEntities, topicClusters)
    const riskFactors = this.identifyRiskFactors(input, basicAnalysis, businessContext)
    const opportunityIndicators = this.identifyOpportunities(input, basicAnalysis, businessContext)

    return {
      ...basicAnalysis,
      namedEntities,
      topicClusters,
      linguisticFeatures,
      businessContext,
      riskFactors,
      opportunityIndicators
    }
  }

  private performBasicAnalysis(input: string): InputAnalysis {
    const words = input.toLowerCase().split(/\s+/)
    const sentences = input.split(/[.!?]+/).filter(s => s.trim().length > 0)
    
    return {
      keywords: this.extractKeywords(input),
      entities: this.extractBasicEntities(input),
      sentiment: this.analyzeSentiment(input),
      complexity: this.assessComplexity(input),
      intent: this.determineIntent(input),
      context: this.extractContext(input),
      urgency: this.assessUrgency(input),
      completeness: this.calculateCompleteness(input)
    }
  }

  private extractNamedEntities(input: string): NamedEntity[] {
    const entities: NamedEntity[] = []
    
    // Money entities
    const moneyRegex = /R\$\s*([\d.,]+)|(\d+(?:\.\d{3})*(?:,\d{2})?)\s*reais?/gi
    let match
    while ((match = moneyRegex.exec(input)) !== null) {
      entities.push({
        text: match[0],
        type: 'MONEY',
        confidence: 0.9,
        startIndex: match.index,
        endIndex: match.index + match[0].length
      })
    }

    // Percentage entities
    const percentRegex = /(\d+(?:,\d+)?)\s*%/g
    while ((match = percentRegex.exec(input)) !== null) {
      entities.push({
        text: match[0],
        type: 'PERCENTAGE',
        confidence: 0.95,
        startIndex: match.index,
        endIndex: match.index + match[0].length
      })
    }

    // Date entities
    const dateRegex = /(\d{1,2}\/\d{1,2}\/\d{4}|\d{1,2}\s+de\s+\w+|\w+\s+\d{1,2})/gi
    while ((match = dateRegex.exec(input)) !== null) {
      entities.push({
        text: match[0],
        type: 'DATE',
        confidence: 0.8,
        startIndex: match.index,
        endIndex: match.index + match[0].length
      })
    }

    // Organization entities (capitalized sequences)
    const orgRegex = /\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b/g
    while ((match = orgRegex.exec(input)) !== null) {
      // Filter out common words that might be capitalized
      const commonWords = ['Instagram', 'Facebook', 'Google', 'YouTube', 'LinkedIn', 'TikTok']
      if (match[0].length > 3 && !commonWords.includes(match[0])) {
        entities.push({
          text: match[0],
          type: 'ORGANIZATION',
          confidence: 0.6,
          startIndex: match.index,
          endIndex: match.index + match[0].length
        })
      }
    }

    return entities
  }

  private identifyTopicClusters(input: string): TopicCluster[] {
    const words = input.toLowerCase().split(/\s+/)
    const clusters: TopicCluster[] = []

    // Marketing cluster
    const marketingWords = words.filter(word => this.marketingTerms.has(word))
    if (marketingWords.length > 0) {
      clusters.push({
        name: 'Marketing Digital',
        keywords: marketingWords,
        relevance: marketingWords.length / words.length,
        category: 'marketing'
      })
    }

    // Business cluster
    const businessWords = words.filter(word => this.businessTerms.has(word))
    if (businessWords.length > 0) {
      clusters.push({
        name: 'Negócios',
        keywords: businessWords,
        relevance: businessWords.length / words.length,
        category: 'business'
      })
    }

    // Technical cluster
    const technicalWords = words.filter(word => this.technicalTerms.has(word))
    if (technicalWords.length > 0) {
      clusters.push({
        name: 'Técnico',
        keywords: technicalWords,
        relevance: technicalWords.length / words.length,
        category: 'technical'
      })
    }

    return clusters.sort((a, b) => b.relevance - a.relevance)
  }

  private analyzeLinguisticFeatures(input: string): LinguisticFeatures {
    const sentences = input.split(/[.!?]+/).filter(s => s.trim().length > 0)
    const words = input.split(/\s+/)
    
    // Count questions and imperatives
    const questionCount = (input.match(/\?/g) || []).length
    const imperativeCount = this.countImperatives(input)
    
    // Calculate average sentence length
    const averageSentenceLength = sentences.length > 0 ? 
      sentences.reduce((sum, s) => sum + s.split(/\s+/).length, 0) / sentences.length : 0

    // Assess formality
    const formalityLevel = this.assessFormality(input)
    
    // Technical complexity based on technical terms
    const technicalWords = words.filter(word => this.technicalTerms.has(word.toLowerCase()))
    const technicalComplexity = technicalWords.length / words.length

    // Emotional intensity based on emotional words
    const emotionalWords = words.filter(word => 
      this.positiveIndicators.has(word.toLowerCase()) || 
      this.negativeIndicators.has(word.toLowerCase())
    )
    const emotionalIntensity = emotionalWords.length / words.length

    // Simple readability score (based on sentence length and word complexity)
    const readabilityScore = this.calculateReadabilityScore(sentences, words)

    return {
      readabilityScore,
      formalityLevel,
      technicalComplexity,
      emotionalIntensity,
      questionCount,
      imperativeCount,
      averageSentenceLength
    }
  }

  private inferBusinessContext(
    input: string, 
    entities: NamedEntity[], 
    clusters: TopicCluster[]
  ): BusinessContext {
    // Infer industry from topic clusters and keywords
    const industry: string[] = []
    clusters.forEach(cluster => {
      if (cluster.category === 'marketing') industry.push('Marketing Digital')
      if (cluster.category === 'business') industry.push('Consultoria')
      if (cluster.category === 'technical') industry.push('Tecnologia')
    })

    // Infer budget range from money entities
    let budgetRange: BusinessContext['budgetRange'] = 'small'
    const moneyEntities = entities.filter(e => e.type === 'MONEY')
    if (moneyEntities.length > 0) {
      const amounts = moneyEntities.map(e => {
        const numStr = e.text.replace(/[^\d,]/g, '').replace(',', '.')
        return parseFloat(numStr) || 0
      })
      const maxAmount = Math.max(...amounts)
      
      if (maxAmount < 5000) budgetRange = 'micro'
      else if (maxAmount < 25000) budgetRange = 'small'
      else if (maxAmount < 100000) budgetRange = 'medium'
      else if (maxAmount < 500000) budgetRange = 'large'
      else budgetRange = 'enterprise'
    }

    // Infer business stage from language and complexity
    let businessStage: BusinessContext['businessStage'] = 'growth'
    const words = input.toLowerCase()
    if (words.includes('startup') || words.includes('começando')) businessStage = 'startup'
    else if (words.includes('empresa') || words.includes('corporação')) businessStage = 'mature'
    else if (words.includes('multinacional') || words.includes('holding')) businessStage = 'enterprise'

    // Infer marketing maturity from technical terms usage
    const technicalCluster = clusters.find(c => c.category === 'technical')
    let marketingMaturity: BusinessContext['marketingMaturity'] = 'intermediate'
    if (!technicalCluster || technicalCluster.relevance < 0.1) marketingMaturity = 'beginner'
    else if (technicalCluster.relevance > 0.3) marketingMaturity = 'advanced'

    // Infer timeframe from urgency and date entities
    let timeframe: BusinessContext['timeframe'] = 'medium-term'
    const urgencyWords = input.toLowerCase().split(/\s+/).filter(word => this.urgencyIndicators.has(word))
    if (urgencyWords.length > 0) timeframe = 'immediate'
    else if (entities.some(e => e.type === 'DATE')) timeframe = 'short-term'

    return {
      industry,
      businessStage,
      marketingMaturity,
      budgetRange,
      timeframe
    }
  }

  private identifyRiskFactors(
    input: string, 
    analysis: InputAnalysis, 
    context: BusinessContext
  ): RiskFactor[] {
    const risks: RiskFactor[] = []

    // Timeline risks
    if (analysis.urgency === 'high' && analysis.complexity === 'advanced') {
      risks.push({
        type: 'timeline',
        description: 'Projeto complexo com prazo apertado pode comprometer qualidade',
        severity: 'high',
        probability: 0.8,
        impact: 0.9,
        mitigation: [
          'Alocar recursos adicionais',
          'Simplificar escopo inicial',
          'Estabelecer marcos intermediários'
        ]
      })
    }

    // Budget risks
    if (context.budgetRange === 'micro' && analysis.complexity === 'advanced') {
      risks.push({
        type: 'budget',
        description: 'Orçamento limitado para projeto complexo',
        severity: 'medium',
        probability: 0.7,
        impact: 0.8,
        mitigation: [
          'Redefinir escopo',
          'Implementação em fases',
          'Buscar orçamento adicional'
        ]
      })
    }

    // Communication risks
    if (analysis.completeness < 0.5) {
      risks.push({
        type: 'communication',
        description: 'Briefing incompleto pode gerar mal-entendidos',
        severity: 'medium',
        probability: 0.6,
        impact: 0.7,
        mitigation: [
          'Reunião de alinhamento urgente',
          'Checklist de informações',
          'Validação constante com cliente'
        ]
      })
    }

    return risks
  }

  private identifyOpportunities(
    input: string, 
    analysis: InputAnalysis, 
    context: BusinessContext
  ): OpportunityIndicator[] {
    const opportunities: OpportunityIndicator[] = []

    // Growth opportunities
    if (context.businessStage === 'startup' && context.budgetRange !== 'micro') {
      opportunities.push({
        type: 'growth',
        description: 'Startup com orçamento adequado tem potencial de crescimento acelerado',
        potential: 'high',
        effort: 'medium',
        timeframe: 'short'
      })
    }

    // Efficiency opportunities
    if (context.marketingMaturity === 'beginner' && analysis.complexity === 'basic') {
      opportunities.push({
        type: 'efficiency',
        description: 'Implementação de processos básicos pode gerar ganhos rápidos',
        potential: 'medium',
        effort: 'low',
        timeframe: 'immediate'
      })
    }

    // Innovation opportunities
    if (context.marketingMaturity === 'advanced' && analysis.keywords.includes('inovação')) {
      opportunities.push({
        type: 'innovation',
        description: 'Cliente avançado aberto a soluções inovadoras',
        potential: 'high',
        effort: 'high',
        timeframe: 'medium'
      })
    }

    return opportunities
  }

  // Helper methods
  private extractKeywords(input: string): string[] {
    const commonWords = new Set(['o', 'a', 'de', 'para', 'com', 'em', 'um', 'uma', 'do', 'da', 'no', 'na', 'por', 'se', 'que', 'como', 'mais', 'mas', 'ou', 'e', 'é', 'são', 'foi', 'ser', 'ter', 'seu', 'sua', 'seus', 'suas'])
    
    const words = input.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 2 && !commonWords.has(word))
    
    const frequency: Record<string, number> = {}
    words.forEach(word => {
      frequency[word] = (frequency[word] || 0) + 1
    })
    
    return Object.entries(frequency)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([word]) => word)
  }

  private extractBasicEntities(input: string): string[] {
    const entities: string[] = []
    
    const moneyRegex = /R\$\s*[\d.,]+|[\d.,]+\s*reais?/gi
    const moneyMatches = input.match(moneyRegex)
    if (moneyMatches) entities.push(...moneyMatches)
    
    const dateRegex = /\d{1,2}\/\d{1,2}\/\d{4}|\d{1,2}\s+de\s+\w+|\w+\s+\d{1,2}/gi
    const dateMatches = input.match(dateRegex)
    if (dateMatches) entities.push(...dateMatches)
    
    const percentRegex = /\d+%/g
    const percentMatches = input.match(percentRegex)
    if (percentMatches) entities.push(...percentMatches)
    
    return [...new Set(entities)]
  }

  private analyzeSentiment(input: string): 'positive' | 'neutral' | 'negative' {
    const words = input.toLowerCase().split(/\s+/)
    let positiveScore = 0
    let negativeScore = 0
    
    words.forEach(word => {
      if (this.positiveIndicators.has(word)) positiveScore++
      if (this.negativeIndicators.has(word)) negativeScore++
    })
    
    if (positiveScore > negativeScore) return 'positive'
    if (negativeScore > positiveScore) return 'negative'
    return 'neutral'
  }

  private assessComplexity(input: string): 'basic' | 'intermediate' | 'advanced' {
    const words = input.toLowerCase().split(/\s+/)
    const technicalTermCount = words.filter(word => this.technicalTerms.has(word)).length
    
    if (input.length < 100 && technicalTermCount === 0) return 'basic'
    if (input.length > 300 || technicalTermCount > 2) return 'advanced'
    return 'intermediate'
  }

  private determineIntent(input: string): string {
    const inputLower = input.toLowerCase()
    
    if (inputLower.includes('briefing') || inputLower.includes('criar')) return 'create_briefing'
    if (inputLower.includes('análise') || inputLower.includes('performance')) return 'analyze_performance'
    if (inputLower.includes('planejar') || inputLower.includes('estratégia')) return 'plan_campaign'
    if (inputLower.includes('otimizar') || inputLower.includes('melhorar')) return 'optimize'
    if (inputLower.includes('relatório') || inputLower.includes('report')) return 'report'
    if (inputLower.includes('problema') || inputLower.includes('erro')) return 'troubleshoot'
    
    return 'general_inquiry'
  }

  private extractContext(input: string): Record<string, any> {
    const context: Record<string, any> = {}
    
    const budgetMatch = input.match(/(?:budget|orçamento).*?R\$\s*([\d.,]+)/i)
    if (budgetMatch) context.budget = budgetMatch[1]
    
    const timelineMatch = input.match(/(\d+)\s*(?:dias?|semanas?|meses?)/i)
    if (timelineMatch) context.timeline = timelineMatch[0]
    
    const audienceMatch = input.match(/(?:público|target|audiência).*?(\d+[-–]\d+\s*anos?)/i)
    if (audienceMatch) context.targetAge = audienceMatch[1]
    
    const channels = ['instagram', 'facebook', 'google', 'youtube', 'linkedin', 'tiktok']
    const mentionedChannels = channels.filter(channel => 
      input.toLowerCase().includes(channel)
    )
    if (mentionedChannels.length > 0) context.channels = mentionedChannels
    
    return context
  }

  private assessUrgency(input: string): 'low' | 'medium' | 'high' {
    const words = input.toLowerCase().split(/\s+/)
    const urgentWords = words.filter(word => this.urgencyIndicators.has(word))
    
    if (urgentWords.length > 0) return 'high'
    if (input.toLowerCase().includes('breve') || input.toLowerCase().includes('próximo')) return 'medium'
    return 'low'
  }

  private calculateCompleteness(input: string): number {
    let score = 0
    const maxScore = 10
    
    if (input.length > 50) score += 2
    if (input.length > 150) score += 2
    if (input.includes('R$') || input.toLowerCase().includes('orçamento')) score += 2
    if (input.match(/\d+\s*(?:dias?|semanas?|meses?)/)) score += 2
    if (input.toLowerCase().includes('público') || input.toLowerCase().includes('target')) score += 1
    if (input.toLowerCase().includes('cliente') || input.toLowerCase().includes('campanha')) score += 1
    
    return Math.min(score / maxScore, 1)
  }

  private countImperatives(input: string): number {
    const imperativePatterns = [
      /\b(?:faça|crie|desenvolva|implemente|execute|realize)\b/gi,
      /\b(?:preciso|quero|gostaria|solicito)\b/gi
    ]
    
    return imperativePatterns.reduce((count, pattern) => {
      const matches = input.match(pattern)
      return count + (matches ? matches.length : 0)
    }, 0)
  }

  private assessFormality(input: string): 'informal' | 'neutral' | 'formal' {
    const informalIndicators = ['oi', 'olá', 'beleza', 'valeu', 'obrigado', 'brigado']
    const formalIndicators = ['prezado', 'cordialmente', 'atenciosamente', 'solicito', 'gostaria']
    
    const words = input.toLowerCase().split(/\s+/)
    const informalCount = words.filter(word => informalIndicators.includes(word)).length
    const formalCount = words.filter(word => formalIndicators.includes(word)).length
    
    if (formalCount > informalCount) return 'formal'
    if (informalCount > formalCount) return 'informal'
    return 'neutral'
  }

  private calculateReadabilityScore(sentences: string[], words: string[]): number {
    if (sentences.length === 0 || words.length === 0) return 0
    
    const avgSentenceLength = words.length / sentences.length
    const complexWords = words.filter(word => word.length > 6).length
    const complexWordRatio = complexWords / words.length
    
    // Simple readability formula (higher score = easier to read)
    return Math.max(0, 100 - (avgSentenceLength * 1.5) - (complexWordRatio * 100))
  }
}
