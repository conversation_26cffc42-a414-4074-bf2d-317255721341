/**
 * Comprehensive Test Suite for Enhanced Agent System
 * 
 * This test suite validates the functionality, quality, and performance
 * of the enhanced agent response generation system.
 */

import { describe, it, expect, beforeEach, jest } from '@jest/globals'
import { EnhancedAgentSystem, generateAgentResponse, analyzeUserInput } from '../enhancedAgentSystem'
import { IntelligenceLayer } from '../intelligenceLayer'
import { ResponseEngine } from '../responseEngine'
import { ResponseTemplateEngine } from '../responseTemplates'
import { AgentKnowledgeBase } from '../knowledgeBase'

describe('Enhanced Agent System', () => {
  let agentSystem: EnhancedAgentSystem

  beforeEach(() => {
    agentSystem = EnhancedAgentSystem.getInstance()
  })

  describe('Input Analysis', () => {
    it('should analyze simple briefing input correctly', () => {
      const input = 'Preciso criar uma campanha para lançamento de produto com orçamento de R$ 50.000'
      const analysis = analyzeUserInput(input)

      expect(analysis.keywords).toContain('campanha')
      expect(analysis.keywords).toContain('produto')
      expect(analysis.intent).toBe('create_briefing')
      expect(analysis.context.budget).toBe('50.000')
      expect(analysis.complexity).toBe('intermediate')
      expect(analysis.completeness).toBeGreaterThan(0.5)
    })

    it('should detect urgency indicators', () => {
      const urgentInput = 'Preciso urgente de uma campanha para hoje'
      const analysis = analyzeUserInput(urgentInput)

      expect(analysis.urgency).toBe('high')
    })

    it('should identify risk factors', () => {
      const riskyInput = 'Campanha complexa urgente sem orçamento definido'
      const analysis = analyzeUserInput(riskyInput)

      expect(analysis.riskFactors.length).toBeGreaterThan(0)
      expect(analysis.riskFactors.some(r => r.type === 'budget' || r.type === 'timeline')).toBe(true)
    })

    it('should extract named entities', () => {
      const input = 'Campanha para Empresa ABC com R$ 100.000 em 30 dias'
      const analysis = analyzeUserInput(input)

      expect(analysis.namedEntities.some(e => e.type === 'MONEY')).toBe(true)
      expect(analysis.namedEntities.some(e => e.type === 'ORGANIZATION')).toBe(true)
    })
  })

  describe('Response Generation', () => {
    it('should generate comprehensive response for atendimento agent', async () => {
      const input = 'Cliente quer campanha de Black Friday para e-commerce com budget R$ 80k'
      const response = await generateAgentResponse('atendimento', input)

      expect(response.agentId).toBe('atendimento')
      expect(response.response.sections.length).toBeGreaterThan(3)
      expect(response.response.summary).toBeTruthy()
      expect(response.response.actionItems.length).toBeGreaterThan(0)
      expect(response.metadata.qualityScore).toBeGreaterThan(0.7)
    })

    it('should generate strategic response for planejamento agent', async () => {
      const input = 'Planejar campanha multi-canal para startup tech B2B'
      const response = await generateAgentResponse('planejamento', input)

      expect(response.agentId).toBe('planejamento')
      expect(response.response.sections.some(s => s.type === 'analysis')).toBe(true)
      expect(response.response.sections.some(s => s.type === 'recommendation')).toBe(true)
      expect(response.formattedOutput).toContain('Estratégica')
    })

    it('should generate performance analysis for midia agent', async () => {
      const input = 'Analisar performance da campanha atual e sugerir otimizações'
      const response = await generateAgentResponse('midia', input)

      expect(response.agentId).toBe('midia')
      expect(response.response.sections.some(s => s.title.includes('Performance'))).toBe(true)
      expect(response.response.sections.some(s => s.title.includes('Otimização'))).toBe(true)
    })
  })

  describe('Quality Validation', () => {
    it('should meet minimum quality thresholds', async () => {
      const input = 'Briefing completo: Cliente XYZ, campanha awareness, público 25-45 anos, budget R$ 100k, prazo 60 dias, canais Instagram e Google Ads'
      const response = await generateAgentResponse('atendimento', input)

      expect(response.metadata.qualityScore).toBeGreaterThan(0.8)
      expect(response.metadata.confidence).toBeGreaterThan(0.7)
      expect(response.metadata.completeness).toBeGreaterThan(0.8)
    })

    it('should flag low quality responses', async () => {
      const input = 'campanha'
      const response = await generateAgentResponse('atendimento', input)

      expect(response.metadata.qualityScore).toBeLessThan(0.8)
      expect(response.metadata.followUpRecommended).toBe(true)
    })

    it('should provide follow-up questions for incomplete inputs', async () => {
      const input = 'Quero fazer marketing'
      const response = await generateAgentResponse('atendimento', input)

      expect(response.response.followUpQuestions.length).toBeGreaterThan(0)
      expect(response.metadata.followUpRecommended).toBe(true)
    })
  })

  describe('Performance Metrics', () => {
    it('should process responses within acceptable time limits', async () => {
      const input = 'Campanha de lançamento para produto inovador'
      const startTime = Date.now()
      
      const response = await generateAgentResponse('planejamento', input)
      const processingTime = Date.now() - startTime

      expect(processingTime).toBeLessThan(5000) // 5 seconds max
      expect(response.metadata.processingTime).toBeLessThan(processingTime)
    })

    it('should calculate accurate read time estimates', async () => {
      const input = 'Briefing detalhado para campanha complexa multi-canal'
      const response = await generateAgentResponse('atendimento', input)

      expect(response.response.estimatedReadTime).toBeGreaterThan(0)
      expect(response.response.estimatedReadTime).toBeLessThan(10) // Reasonable upper bound
    })
  })

  describe('Agent Specialization', () => {
    it('should provide agent-specific capabilities', () => {
      const capabilities = agentSystem.getAgentCapabilities('atendimento')
      
      expect(capabilities).toBeTruthy()
      expect(capabilities!.specializations).toContain('briefing')
      expect(capabilities!.knowledgeAreas.length).toBeGreaterThan(0)
    })

    it('should return null for invalid agent', () => {
      const capabilities = agentSystem.getAgentCapabilities('invalid-agent')
      expect(capabilities).toBeNull()
    })
  })

  describe('Batch Processing', () => {
    it('should process multiple inputs efficiently', async () => {
      const inputs = [
        'Campanha para produto A',
        'Análise de performance',
        'Planejamento estratégico'
      ]

      const responses = await agentSystem.generateBatchResponses('atendimento', inputs)

      expect(responses.length).toBe(3)
      responses.forEach(response => {
        expect(response.agentId).toBe('atendimento')
        expect(response.response.sections.length).toBeGreaterThan(0)
      })
    })

    it('should handle errors gracefully in batch processing', async () => {
      const inputs = [
        'Valid input',
        '', // Invalid empty input
        'Another valid input'
      ]

      const responses = await agentSystem.generateBatchResponses('atendimento', inputs)

      // Should continue processing despite errors
      expect(responses.length).toBeGreaterThan(0)
      expect(responses.length).toBeLessThanOrEqual(3)
    })
  })

  describe('Error Handling', () => {
    it('should throw error for invalid agent ID', async () => {
      await expect(generateAgentResponse('invalid-agent', 'test input'))
        .rejects.toThrow('Agent configuration not found')
    })

    it('should handle empty input gracefully', async () => {
      const response = await generateAgentResponse('atendimento', '')
      
      expect(response.analysis.completeness).toBe(0)
      expect(response.metadata.followUpRecommended).toBe(true)
    })
  })

  describe('Response Formatting', () => {
    it('should format response according to agent style', async () => {
      const input = 'Campanha completa para cliente premium'
      const response = await generateAgentResponse('atendimento', input)

      expect(response.formattedOutput).toContain('#') // Markdown headers
      expect(response.formattedOutput).toContain('##') // Section headers
      expect(response.formattedOutput).toContain('Confiança:') // Metadata
    })

    it('should include action items in formatted output', async () => {
      const input = 'Briefing para campanha urgente'
      const response = await generateAgentResponse('atendimento', input)

      if (response.response.actionItems.length > 0) {
        expect(response.formattedOutput).toContain('- [ ]') // Checkbox format
      }
    })
  })

  describe('Context Awareness', () => {
    it('should adapt response based on user context', async () => {
      const input = 'Campanha para startup'
      const userContext = { 
        company_stage: 'startup',
        budget_range: 'small',
        industry: 'tech'
      }

      const response = await generateAgentResponse('planejamento', input, userContext)

      expect(response.analysis.businessContext.businessStage).toBe('startup')
    })

    it('should maintain context across related inputs', async () => {
      const input1 = 'Cliente ABC quer campanha de awareness'
      const input2 = 'Agora precisa de cronograma para o cliente ABC'

      const response1 = await generateAgentResponse('atendimento', input1)
      const response2 = await generateAgentResponse('planejamento', input2)

      // Both should recognize the client context
      expect(response1.analysis.entities.some(e => e.includes('ABC'))).toBe(true)
      expect(response2.analysis.entities.some(e => e.includes('ABC'))).toBe(true)
    })
  })

  describe('Integration Tests', () => {
    it('should integrate all system components correctly', async () => {
      const input = 'Briefing completo: Empresa TechCorp, lançamento produto B2B, público CTOs e desenvolvedores, budget R$ 200k, prazo 90 dias, canais LinkedIn e Google Ads, objetivo 500 leads qualificados'
      
      const response = await generateAgentResponse('atendimento', input)

      // Verify all components worked together
      expect(response.analysis.namedEntities.length).toBeGreaterThan(0) // Intelligence layer
      expect(response.response.sections.length).toBeGreaterThan(5) // Template engine
      expect(response.metadata.qualityScore).toBeGreaterThan(0.8) // Quality validation
      expect(response.formattedOutput.length).toBeGreaterThan(500) // Comprehensive output
    })
  })
})

describe('Individual Component Tests', () => {
  describe('Intelligence Layer', () => {
    let intelligenceLayer: IntelligenceLayer

    beforeEach(() => {
      intelligenceLayer = IntelligenceLayer.getInstance()
    })

    it('should extract keywords correctly', () => {
      const input = 'Campanha de marketing digital para e-commerce'
      const analysis = intelligenceLayer.performAdvancedAnalysis(input)

      expect(analysis.keywords).toContain('campanha')
      expect(analysis.keywords).toContain('marketing')
      expect(analysis.keywords).toContain('digital')
    })

    it('should identify topic clusters', () => {
      const input = 'ROI da campanha no Instagram com CPC baixo'
      const analysis = intelligenceLayer.performAdvancedAnalysis(input)

      expect(analysis.topicClusters.some(c => c.category === 'marketing')).toBe(true)
    })

    it('should analyze linguistic features', () => {
      const input = 'Preciso urgentemente de uma campanha muito complexa para o cliente mais importante!'
      const analysis = intelligenceLayer.performAdvancedAnalysis(input)

      expect(analysis.linguisticFeatures.emotionalIntensity).toBeGreaterThan(0)
      expect(analysis.linguisticFeatures.questionCount).toBe(0)
    })
  })

  describe('Response Engine', () => {
    let responseEngine: ResponseEngine

    beforeEach(() => {
      responseEngine = ResponseEngine.getInstance()
    })

    it('should analyze input correctly', () => {
      const input = 'Campanha para Black Friday com budget limitado'
      const analysis = responseEngine.analyzeInput(input)

      expect(analysis.keywords.length).toBeGreaterThan(0)
      expect(analysis.urgency).toBeDefined()
      expect(analysis.complexity).toBeDefined()
    })

    it('should generate response sections', () => {
      const input = 'Briefing para campanha de awareness'
      const analysis = responseEngine.analyzeInput(input)
      const response = responseEngine.generateResponse('atendimento', input, analysis)

      expect(response.sections.length).toBeGreaterThan(0)
      expect(response.summary).toBeTruthy()
      expect(response.confidence).toBeGreaterThan(0)
    })
  })

  describe('Knowledge Base', () => {
    let knowledgeBase: AgentKnowledgeBase

    beforeEach(() => {
      knowledgeBase = AgentKnowledgeBase.getInstance()
    })

    it('should provide agent knowledge', () => {
      const knowledge = knowledgeBase.getAgentKnowledge('atendimento')

      expect(knowledge).toBeTruthy()
      expect(knowledge!.specializations.length).toBeGreaterThan(0)
      expect(knowledge!.bestPractices.length).toBeGreaterThan(0)
    })

    it('should return applicable scenarios', () => {
      const analysis = { keywords: ['briefing'], intent: 'create_briefing' } as any
      const scenarios = knowledgeBase.getApplicableScenarios('atendimento', analysis)

      expect(scenarios.length).toBeGreaterThan(0)
    })
  })
})

describe('Edge Cases and Stress Tests', () => {
  it('should handle very long inputs', async () => {
    const longInput = 'Campanha '.repeat(1000) + 'para cliente especial'
    const response = await generateAgentResponse('atendimento', longInput)

    expect(response.metadata.processingTime).toBeLessThan(10000) // 10 seconds max
    expect(response.response.sections.length).toBeGreaterThan(0)
  })

  it('should handle special characters and emojis', async () => {
    const input = 'Campanha 🚀 para cliente @TechCorp com #hashtag e símbolos especiais: R$ 50.000,00 (50% desconto)'
    const response = await generateAgentResponse('atendimento', input)

    expect(response.analysis.namedEntities.some(e => e.type === 'MONEY')).toBe(true)
    expect(response.response.sections.length).toBeGreaterThan(0)
  })

  it('should handle multilingual content', async () => {
    const input = 'Campaign for international client with budget $50,000 USD'
    const response = await generateAgentResponse('atendimento', input)

    expect(response.response.sections.length).toBeGreaterThan(0)
    expect(response.metadata.qualityScore).toBeGreaterThan(0.5)
  })

  it('should maintain performance under concurrent requests', async () => {
    const promises = Array.from({ length: 10 }, (_, i) => 
      generateAgentResponse('atendimento', `Campanha ${i} para teste de concorrência`)
    )

    const responses = await Promise.all(promises)

    expect(responses.length).toBe(10)
    responses.forEach((response, i) => {
      expect(response.input).toContain(`Campanha ${i}`)
      expect(response.metadata.processingTime).toBeLessThan(5000)
    })
  })
})
