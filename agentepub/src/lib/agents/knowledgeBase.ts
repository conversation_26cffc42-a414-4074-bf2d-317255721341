/**
 * Agent Knowledge Base and Specialization System
 * 
 * This module contains specialized knowledge, best practices, and response templates
 * for each agent type, enabling them to provide expert-level insights and recommendations.
 */

import { InputAnalysis, ResponseSection } from './responseEngine'

export interface AgentKnowledge {
  specializations: string[]
  commonScenarios: Scenario[]
  bestPractices: BestPractice[]
  troubleshooting: TroubleshootingGuide[]
  templates: ResponseTemplate[]
  industryInsights: InsightRule[]
  warningTriggers: WarningTrigger[]
}

export interface Scenario {
  id: string
  name: string
  description: string
  triggers: string[]
  response: string
  examples: string[]
  relatedTopics: string[]
}

export interface BestPractice {
  category: string
  title: string
  description: string
  implementation: string[]
  benefits: string[]
  commonMistakes: string[]
}

export interface TroubleshootingGuide {
  problem: string
  symptoms: string[]
  causes: string[]
  solutions: string[]
  prevention: string[]
}

export interface ResponseTemplate {
  name: string
  structure: TemplateSection[]
  applicableWhen: (analysis: InputAnalysis) => boolean
}

export interface TemplateSection {
  title: string
  contentGenerator: (analysis: InputAnalysis, input: string) => string
  priority: number
  required: boolean
}

export interface InsightRule {
  condition: (analysis: InputAnalysis) => boolean
  insight: string
  actionable: boolean
  priority: 'high' | 'medium' | 'low'
}

export interface WarningTrigger {
  condition: (analysis: InputAnalysis) => boolean
  warning: string
  severity: 'critical' | 'important' | 'advisory'
  mitigation: string[]
}

export class AgentKnowledgeBase {
  private static instance: AgentKnowledgeBase
  private knowledgeMap: Map<string, AgentKnowledge> = new Map()

  private constructor() {
    this.initializeKnowledge()
  }

  public static getInstance(): AgentKnowledgeBase {
    if (!AgentKnowledgeBase.instance) {
      AgentKnowledgeBase.instance = new AgentKnowledgeBase()
    }
    return AgentKnowledgeBase.instance
  }

  public getAgentKnowledge(agentId: string): AgentKnowledge | undefined {
    return this.knowledgeMap.get(agentId)
  }

  public getApplicableScenarios(agentId: string, analysis: InputAnalysis): Scenario[] {
    const knowledge = this.knowledgeMap.get(agentId)
    if (!knowledge) return []

    return knowledge.commonScenarios.filter(scenario =>
      scenario.triggers.some(trigger =>
        analysis.keywords.some(keyword => keyword.includes(trigger.toLowerCase())) ||
        analysis.intent.includes(trigger.toLowerCase())
      )
    )
  }

  public getBestPractices(agentId: string, analysis: InputAnalysis): BestPractice[] {
    const knowledge = this.knowledgeMap.get(agentId)
    if (!knowledge) return []

    // Return relevant best practices based on context
    return knowledge.bestPractices.filter(practice =>
      analysis.keywords.some(keyword => 
        practice.category.toLowerCase().includes(keyword) ||
        practice.title.toLowerCase().includes(keyword)
      )
    ).slice(0, 3) // Limit to top 3 most relevant
  }

  public getInsights(agentId: string, analysis: InputAnalysis): InsightRule[] {
    const knowledge = this.knowledgeMap.get(agentId)
    if (!knowledge) return []

    return knowledge.industryInsights
      .filter(rule => rule.condition(analysis))
      .sort((a, b) => {
        const priorityOrder = { high: 3, medium: 2, low: 1 }
        return priorityOrder[b.priority] - priorityOrder[a.priority]
      })
  }

  public getWarnings(agentId: string, analysis: InputAnalysis): WarningTrigger[] {
    const knowledge = this.knowledgeMap.get(agentId)
    if (!knowledge) return []

    return knowledge.warningTriggers.filter(trigger => trigger.condition(analysis))
  }

  public getTroubleshootingGuides(agentId: string, analysis: InputAnalysis): TroubleshootingGuide[] {
    const knowledge = this.knowledgeMap.get(agentId)
    if (!knowledge) return []

    // Return guides that match problem indicators in the input
    return knowledge.troubleshooting.filter(guide =>
      guide.symptoms.some(symptom =>
        analysis.keywords.some(keyword => keyword.includes(symptom.toLowerCase()))
      )
    )
  }

  private initializeKnowledge(): void {
    // Initialize Atendimento Agent Knowledge
    this.knowledgeMap.set('atendimento', {
      specializations: [
        'Gestão de Briefings',
        'Comunicação com Cliente',
        'Cronogramas de Aprovação',
        'Gestão de Expectativas',
        'Documentação de Projetos'
      ],
      commonScenarios: [
        {
          id: 'briefing_incompleto',
          name: 'Briefing Incompleto',
          description: 'Cliente forneceu informações insuficientes para iniciar o projeto',
          triggers: ['briefing', 'incompleto', 'faltando', 'informação'],
          response: 'Identifiquei que algumas informações essenciais estão faltando no briefing.',
          examples: [
            'Briefing sem definição de público-alvo',
            'Orçamento não especificado',
            'Prazo não definido'
          ],
          relatedTopics: ['Coleta de Informações', 'Validação de Briefing', 'Comunicação com Cliente']
        },
        {
          id: 'prazo_apertado',
          name: 'Prazo Apertado',
          description: 'Cliente solicita entrega em prazo muito curto',
          triggers: ['urgente', 'rápido', 'hoje', 'amanhã', 'prazo'],
          response: 'Detectei uma solicitação com prazo apertado que requer atenção especial.',
          examples: [
            'Campanha para lançar em 48h',
            'Material para evento na próxima semana',
            'Aprovação urgente necessária'
          ],
          relatedTopics: ['Gestão de Cronograma', 'Priorização', 'Recursos Adicionais']
        }
      ],
      bestPractices: [
        {
          category: 'Briefing',
          title: 'Estruturação Completa de Briefings',
          description: 'Todo briefing deve conter informações essenciais para evitar retrabalho',
          implementation: [
            'Sempre validar objetivo principal',
            'Confirmar público-alvo detalhado',
            'Estabelecer orçamento claro',
            'Definir cronograma realista',
            'Documentar canais de aprovação'
          ],
          benefits: [
            'Reduz retrabalho em 60%',
            'Melhora satisfação do cliente',
            'Acelera processo de aprovação',
            'Diminui mal-entendidos'
          ],
          commonMistakes: [
            'Assumir informações não explícitas',
            'Não validar orçamento real',
            'Ignorar restrições do cliente',
            'Não documentar mudanças'
          ]
        },
        {
          category: 'Comunicação',
          title: 'Gestão Proativa de Expectativas',
          description: 'Manter cliente informado previne problemas e builds confiança',
          implementation: [
            'Updates semanais obrigatórios',
            'Alertas antecipados sobre riscos',
            'Documentação de todas as decisões',
            'Canal direto para dúvidas'
          ],
          benefits: [
            'Aumenta confiança do cliente',
            'Reduz ansiedade e pressão',
            'Facilita resolução de problemas',
            'Melhora relacionamento de longo prazo'
          ],
          commonMistakes: [
            'Comunicar apenas quando há problemas',
            'Usar linguagem muito técnica',
            'Não confirmar entendimento',
            'Deixar cliente sem resposta'
          ]
        }
      ],
      troubleshooting: [
        {
          problem: 'Cliente não responde aprovações',
          symptoms: ['silêncio prolongado', 'aprovações pendentes', 'cronograma atrasado'],
          causes: [
            'Sobrecarga do cliente',
            'Falta de clareza na solicitação',
            'Processo de aprovação interno complexo',
            'Insatisfação não comunicada'
          ],
          solutions: [
            'Ligar diretamente para o cliente',
            'Simplificar processo de aprovação',
            'Oferecer reunião de alinhamento',
            'Criar cronograma de follow-ups'
          ],
          prevention: [
            'Estabelecer SLA de resposta',
            'Mapear processo interno do cliente',
            'Criar lembretes automáticos',
            'Ter contato backup'
          ]
        }
      ],
      templates: [
        {
          name: 'Briefing Estruturado',
          structure: [
            {
              title: '📋 Resumo Executivo',
              contentGenerator: (analysis, input) => this.generateExecutiveSummary(analysis, input),
              priority: 10,
              required: true
            },
            {
              title: '🎯 Objetivos e Metas',
              contentGenerator: (analysis, input) => this.generateObjectives(analysis, input),
              priority: 9,
              required: true
            },
            {
              title: '👥 Público-Alvo',
              contentGenerator: (analysis, input) => this.generateTargetAudience(analysis, input),
              priority: 8,
              required: true
            }
          ],
          applicableWhen: (analysis) => analysis.intent.includes('briefing') || analysis.keywords.includes('briefing')
        }
      ],
      industryInsights: [
        {
          condition: (analysis) => analysis.context.budget && parseFloat(analysis.context.budget.replace(/[^\d]/g, '')) < 10000,
          insight: 'Para orçamentos menores, recomendo focar em 1-2 canais principais para maximizar impacto.',
          actionable: true,
          priority: 'high'
        },
        {
          condition: (analysis) => analysis.urgency === 'high',
          insight: 'Projetos urgentes têm 40% mais chance de problemas. Considere recursos adicionais.',
          actionable: true,
          priority: 'high'
        }
      ],
      warningTriggers: [
        {
          condition: (analysis) => analysis.completeness < 0.5,
          warning: 'Briefing incompleto detectado. Informações essenciais estão faltando.',
          severity: 'important',
          mitigation: [
            'Solicitar reunião de alinhamento',
            'Enviar checklist de informações necessárias',
            'Agendar call de briefing completo'
          ]
        },
        {
          condition: (analysis) => analysis.urgency === 'high' && !analysis.context.budget,
          warning: 'Projeto urgente sem orçamento definido pode gerar problemas de execução.',
          severity: 'critical',
          mitigation: [
            'Definir orçamento mínimo imediatamente',
            'Estabelecer escopo reduzido',
            'Comunicar riscos ao cliente'
          ]
        }
      ]
    })

    // Initialize Planejamento Agent Knowledge
    this.knowledgeMap.set('planejamento', {
      specializations: [
        'Estratégia de Campanhas',
        'Cronogramas Inteligentes',
        'Análise de Concorrência',
        'Distribuição de Budget',
        'Otimização de Performance'
      ],
      commonScenarios: [
        {
          id: 'campanha_multicanal',
          name: 'Campanha Multi-canal',
          description: 'Planejamento de campanha que utiliza múltiplos canais de mídia',
          triggers: ['multicanal', 'instagram', 'facebook', 'google', 'canais'],
          response: 'Identificada necessidade de estratégia multi-canal integrada.',
          examples: [
            'Campanha usando Instagram + Google Ads',
            'Estratégia omnichannel completa',
            'Integração online e offline'
          ],
          relatedTopics: ['Integração de Canais', 'Attribution Modeling', 'Budget Allocation']
        }
      ],
      bestPractices: [
        {
          category: 'Estratégia',
          title: 'Planejamento Baseado em Dados',
          description: 'Usar dados históricos e benchmarks para criar estratégias mais eficazes',
          implementation: [
            'Analisar performance histórica',
            'Benchmarking com concorrentes',
            'Definir KPIs específicos',
            'Criar hipóteses testáveis'
          ],
          benefits: [
            'Melhora ROI em até 35%',
            'Reduz tempo de otimização',
            'Aumenta previsibilidade',
            'Facilita tomada de decisão'
          ],
          commonMistakes: [
            'Ignorar dados históricos',
            'Copiar estratégias sem contexto',
            'Não definir métricas claras',
            'Não testar hipóteses'
          ]
        }
      ],
      troubleshooting: [],
      templates: [],
      industryInsights: [],
      warningTriggers: []
    })

    // Initialize Mídia Agent Knowledge
    this.knowledgeMap.set('midia', {
      specializations: [
        'Análise de Performance',
        'Otimização de Campanhas',
        'Relatórios Avançados',
        'Gestão de Budget',
        'Targeting e Segmentação'
      ],
      commonScenarios: [],
      bestPractices: [],
      troubleshooting: [],
      templates: [],
      industryInsights: [],
      warningTriggers: []
    })
  }

  // Template content generators
  private generateExecutiveSummary(analysis: InputAnalysis, input: string): string {
    return `**Projeto:** ${analysis.context.campaign || 'Campanha identificada no briefing'}
**Complexidade:** ${analysis.complexity}
**Prazo estimado:** ${analysis.context.timeline || '30 dias (recomendado)'}
**Status:** Briefing analisado e estruturado`
  }

  private generateObjectives(analysis: InputAnalysis, input: string): string {
    const objectives = []
    if (analysis.keywords.includes('lançamento')) objectives.push('• Lançamento de produto/serviço')
    if (analysis.keywords.includes('awareness')) objectives.push('• Aumentar conhecimento da marca')
    if (analysis.keywords.includes('vendas')) objectives.push('• Gerar vendas diretas')
    if (objectives.length === 0) objectives.push('• Objetivo principal identificado no briefing')
    
    return objectives.join('\n')
  }

  private generateTargetAudience(analysis: InputAnalysis, input: string): string {
    let audience = '• **Idade:** '
    if (analysis.context.targetAge) {
      audience += analysis.context.targetAge
    } else {
      audience += '25-45 anos (sugerido)'
    }
    
    audience += '\n• **Localização:** Principais centros urbanos'
    audience += '\n• **Comportamento:** Ativo em redes sociais'
    
    return audience
  }
}
