'use client'

import { useEffect, useState } from 'react'
import { useSearchParams } from 'next/navigation'
import ProtectedRoute from '@/components/auth/ProtectedRoute'
import DashboardLayout from '@/components/dashboard/DashboardLayout'
import { useAuth } from '@/contexts/AuthContext'
import {
  PlusIcon,
  ChartBarIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  DocumentTextIcon,
  UserGroupIcon,
  CalendarDaysIcon
} from '@heroicons/react/24/outline'

export default function DashboardPage() {
  const { user, profile, loading } = useAuth()
  const searchParams = useSearchParams()
  const [showNewCampaignModal, setShowNewCampaignModal] = useState(false)

  useEffect(() => {
    if (searchParams.get('newCampaign') === 'true') {
      setShowNewCampaignModal(true)
    }
  }, [searchParams])

  // Show loading state while auth is being determined
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Carregando dashboard...</p>
        </div>
      </div>
    )
  }

  const stats = [
    {
      name: 'Campanhas Ativas',
      value: '3',
      icon: ChartBarIcon,
      color: 'text-blue-600',
      bg: 'bg-blue-100'
    },
    {
      name: 'Materiais Gerados',
      value: '12',
      icon: DocumentTextIcon,
      color: 'text-green-600',
      bg: 'bg-green-100'
    },
    {
      name: 'Prazos Próximos',
      value: '2',
      icon: ClockIcon,
      color: 'text-yellow-600',
      bg: 'bg-yellow-100'
    },
    {
      name: 'Alertas',
      value: '1',
      icon: ExclamationTriangleIcon,
      color: 'text-red-600',
      bg: 'bg-red-100'
    }
  ]

  const recentActivities = [
    {
      id: 1,
      type: 'briefing',
      title: 'Briefing processado: Campanha Black Friday',
      time: '2 horas atrás',
      agent: 'Atendimento'
    },
    {
      id: 2,
      type: 'cronograma',
      title: 'Cronograma gerado: Lançamento App',
      time: '4 horas atrás',
      agent: 'Planejamento'
    },
    {
      id: 3,
      type: 'relatorio',
      title: 'Relatório de performance: Campanha Verão',
      time: '1 dia atrás',
      agent: 'Mídia'
    }
  ]

  const quickActions = [
    {
      name: 'Criar Campanha',
      description: 'Inicie uma nova campanha',
      icon: PlusIcon,
      action: () => setShowNewCampaignModal(true)
    },
    {
      name: 'Processar Briefing',
      description: 'Use o agente de atendimento',
      icon: UserGroupIcon,
      action: () => window.location.href = '/dashboard/agentes'
    },
    {
      name: 'Gerar Cronograma',
      description: 'Use o agente de planejamento',
      icon: CalendarDaysIcon,
      action: () => window.location.href = '/dashboard/agentes'
    },
    {
      name: 'Analisar Performance',
      description: 'Use o agente de mídia',
      icon: ChartBarIcon,
      action: () => window.location.href = '/dashboard/agentes'
    }
  ]

  return (
    <ProtectedRoute>
      <DashboardLayout>
        <div className="space-y-8">
          {/* Welcome Section */}
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Bem-vindo, {profile?.full_name || user?.email}!
            </h1>
            <p className="text-gray-600 mt-1">
              {profile?.area ? `Área: ${profile.area.charAt(0).toUpperCase() + profile.area.slice(1)}` : 'Configure seu perfil para começar'}
            </p>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {stats.map((stat) => (
              <div key={stat.name} className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center">
                  <div className={`flex-shrink-0 ${stat.bg} rounded-md p-3`}>
                    <stat.icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">{stat.name}</p>
                    <p className="text-2xl font-semibold text-gray-900">{stat.value}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Quick Actions */}
          <div>
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Ações Rápidas</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {quickActions.map((action) => (
                <button
                  key={action.name}
                  onClick={action.action}
                  className="bg-white rounded-lg shadow p-6 text-left hover:shadow-md transition-shadow"
                >
                  <div className="flex items-center mb-3">
                    <action.icon className="h-6 w-6 text-primary-600" />
                    <h3 className="ml-2 font-medium text-gray-900">{action.name}</h3>
                  </div>
                  <p className="text-sm text-gray-600">{action.description}</p>
                </button>
              ))}
            </div>
          </div>

          {/* Recent Activities */}
          <div>
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Atividades Recentes</h2>
            <div className="bg-white rounded-lg shadow">
              <div className="divide-y divide-gray-200">
                {recentActivities.map((activity) => (
                  <div key={activity.id} className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-900">{activity.title}</p>
                        <p className="text-sm text-gray-500">Agente: {activity.agent}</p>
                      </div>
                      <p className="text-sm text-gray-500">{activity.time}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Getting Started */}
          {!profile?.area && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-blue-900 mb-2">Complete seu perfil</h3>
              <p className="text-blue-700 mb-4">
                Defina sua área de atuação para receber recomendações personalizadas dos agentes.
              </p>
              <button className="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors">
                Configurar Perfil
              </button>
            </div>
          )}
        </div>

        {/* New Campaign Modal */}
        {showNewCampaignModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg max-w-md w-full p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Nova Campanha</h3>
              <p className="text-gray-600 mb-4">
                Funcionalidade em desenvolvimento. Em breve você poderá criar campanhas diretamente aqui!
              </p>
              <div className="flex gap-3">
                <button
                  onClick={() => setShowNewCampaignModal(false)}
                  className="flex-1 bg-gray-200 text-gray-800 py-2 px-4 rounded-md font-medium hover:bg-gray-300 transition-colors"
                >
                  Fechar
                </button>
                <button
                  onClick={() => {
                    setShowNewCampaignModal(false)
                    window.location.href = '/teste'
                  }}
                  className="flex-1 bg-primary-600 text-white py-2 px-4 rounded-md font-medium hover:bg-primary-700 transition-colors"
                >
                  Testar Agentes
                </button>
              </div>
            </div>
          </div>
        )}
      </DashboardLayout>
    </ProtectedRoute>
  )
}
