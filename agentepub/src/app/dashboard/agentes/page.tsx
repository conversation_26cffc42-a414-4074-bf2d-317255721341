'use client'

import { useState } from 'react'
import ProtectedRoute from '@/components/auth/ProtectedRoute'
import DashboardLayout from '@/components/dashboard/DashboardLayout'
import {
  UserGroupIcon,
  CalendarDaysIcon,
  ChartBarIcon,
  SparklesIcon,
  DocumentTextIcon,
  ArrowRightIcon
} from '@heroicons/react/24/outline'

const agents = [
  {
    id: 'atendimento',
    name: 'Agente de Atendimento',
    description: 'Organiza briefings, acompanha prazos e mantém a comunicação com clientes sempre em dia.',
    icon: UserGroupIcon,
    color: 'blue',
    capabilities: [
      'Estrutura briefings automaticamente',
      'Cria cronogramas de aprovação',
      'Monitora prazos e entregas',
      'Gera relatórios de status'
    ],
    inputPlaceholder: 'Cole aqui o briefing do cliente ou descreva a demanda...',
    examples: [
      'Briefing: Campanha para lançamento de produto X, público jovem 18-25 anos, budget R$ 50k',
      'Cliente solicitou alterações no cronograma da campanha Y',
      'Preciso organizar as aprovações para a campanha de Black Friday'
    ]
  },
  {
    id: 'planejamento',
    name: 'Agente de Planejamento',
    description: 'Gera cronogramas inteligentes, insights estratégicos e organiza toda a execução da campanha.',
    icon: CalendarDaysIcon,
    color: 'green',
    capabilities: [
      'Cronogramas automáticos',
      'Análise de concorrência',
      'Sugestões de estratégia',
      'Distribuição de budget'
    ],
    inputPlaceholder: 'Descreva o projeto ou campanha que precisa ser planejada...',
    examples: [
      'Campanha de 60 dias para lançamento de app, budget R$ 100k, 3 fases',
      'Planejamento estratégico para Black Friday, múltiplos canais',
      'Cronograma para campanha institucional com prazo apertado'
    ]
  },
  {
    id: 'midia',
    name: 'Agente de Mídia',
    description: 'Analisa performance, sugere otimizações e gera relatórios com insights acionáveis.',
    icon: ChartBarIcon,
    color: 'purple',
    capabilities: [
      'Análise de KPIs',
      'Sugestões de otimização',
      'Relatórios automatizados',
      'Alertas de performance'
    ],
    inputPlaceholder: 'Cole dados de performance ou descreva a campanha para análise...',
    examples: [
      'CTR: 2.1%, CPC: R$ 0.85, Budget gasto: R$ 15k de R$ 30k',
      'Campanha Instagram com baixo engajamento, preciso otimizar',
      'Análise de performance da campanha de verão nos últimos 30 dias'
    ]
  }
]

export default function AgentesPage() {
  const [selectedAgent, setSelectedAgent] = useState<string | null>(null)
  const [input, setInput] = useState('')
  const [isProcessing, setIsProcessing] = useState(false)
  const [result, setResult] = useState<string | null>(null)

  const currentAgent = agents.find(agent => agent.id === selectedAgent)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!input.trim() || !currentAgent) return

    setIsProcessing(true)

    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 2000))

    // Generate simulated result based on agent type
    const simulatedResult = generateAgentResult(currentAgent.id, input)
    setResult(simulatedResult)
    setIsProcessing(false)
  }

  const generateAgentResult = (agentId: string, inputText: string) => {
    switch (agentId) {
      case 'atendimento':
        return `## 📋 Briefing Estruturado

**Status:** Processado com sucesso
**Data:** ${new Date().toLocaleDateString('pt-BR')}

### Informações Principais
• **Objetivo:** Identificado no briefing
• **Público-alvo:** Definido conforme descrição
• **Prazo:** Cronograma sugerido de 30 dias
• **Budget:** Distribuição recomendada

### Próximas Ações
1. ✅ Validar informações com cliente
2. ⏳ Definir cronograma detalhado
3. ⏳ Alinhar expectativas de entrega
4. ⏳ Iniciar produção de materiais

### Alertas
• Confirmar budget final
• Definir pontos de aprovação
• Estabelecer canais de comunicação

**Recomendação:** Agendar reunião de alinhamento em 24h`

      case 'planejamento':
        return `## 📅 Cronograma Estratégico

**Projeto:** Baseado no input fornecido
**Duração:** 4 semanas
**Fases:** 3 principais

### Fase 1: Planejamento (Semana 1)
• Definição de personas
• Estratégia de conteúdo
• Aprovação de conceitos
• Setup de campanhas

### Fase 2: Execução (Semanas 2-3)
• Produção de materiais
• Configuração de mídia
• Testes A/B iniciais
• Lançamento gradual

### Fase 3: Otimização (Semana 4)
• Análise de performance
• Ajustes de targeting
• Relatórios finais
• Planejamento de continuidade

### Distribuição de Budget
• Mídia paga: 60%
• Produção: 25%
• Ferramentas: 10%
• Contingência: 5%

### KPIs Sugeridos
• Alcance: 50k+ pessoas
• Engajamento: 2.5%+
• Conversões: 150+ leads`

      case 'midia':
        return `## 📊 Análise de Performance

**Período:** Últimos 30 dias
**Status:** Análise concluída

### Métricas Principais
• **Impressões:** 125.4k (+15% vs período anterior)
• **Cliques:** 2.8k (CTR: 2.23%)
• **CPC Médio:** R$ 0.92
• **Conversões:** 89 (Taxa: 3.18%)

### Insights Identificados
🔍 **Oportunidades:**
• Horários 18h-21h têm melhor performance
• Público feminino 25-34 anos converte mais
• Posts com vídeo têm 40% mais engajamento

⚠️ **Pontos de Atenção:**
• CPC aumentou 12% na última semana
• Taxa de rejeição alta em mobile (65%)
• Saturação do público em SP

### Recomendações
1. **Otimização de Horários:** Concentrar budget 18h-21h
2. **Segmentação:** Focar em mulheres 25-34 anos
3. **Criativos:** Priorizar conteúdo em vídeo
4. **Expansão:** Testar novos mercados (RJ, BH)

### Próximos Passos
• Implementar otimizações sugeridas
• Criar novos criativos em vídeo
• Expandir targeting geográfico
• Monitorar performance semanal`

      default:
        return 'Resultado não disponível para este agente.'
    }
  }

  const resetAgent = () => {
    setSelectedAgent(null)
    setInput('')
    setResult(null)
  }

  if (!selectedAgent) {
    return (
      <ProtectedRoute>
        <DashboardLayout>
          <div className="space-y-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Agentes Inteligentes</h1>
              <p className="text-gray-600">Escolha um agente especializado para ajudar com sua tarefa</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {agents.map((agent) => (
                <div key={agent.id} className="bg-white rounded-lg shadow hover:shadow-md transition-shadow cursor-pointer" onClick={() => setSelectedAgent(agent.id)}>
                  <div className="p-6">
                    <div className="flex items-center gap-3 mb-4">
                      <div className={
                        agent.id === 'atendimento' ? 'bg-blue-100 p-3 rounded-lg' :
                          agent.id === 'planejamento' ? 'bg-green-100 p-3 rounded-lg' :
                            'bg-purple-100 p-3 rounded-lg'
                      }>
                        <agent.icon className={
                          agent.id === 'atendimento' ? 'h-6 w-6 text-blue-600' :
                            agent.id === 'planejamento' ? 'h-6 w-6 text-green-600' :
                              'h-6 w-6 text-purple-600'
                        } />
                      </div>
                      <h3 className="font-semibold text-gray-900">{agent.name}</h3>
                    </div>

                    <p className="text-gray-600 mb-4">{agent.description}</p>

                    <div className="space-y-2 mb-6">
                      {agent.capabilities.map((capability, idx) => (
                        <div key={idx} className="flex items-center gap-2">
                          <div className="w-1.5 h-1.5 bg-primary-500 rounded-full"></div>
                          <span className="text-sm text-gray-700">{capability}</span>
                        </div>
                      ))}
                    </div>

                    <button className="w-full bg-blue-600 text-white py-3 px-4 rounded-md font-semibold hover:bg-blue-700 transition-colors flex items-center justify-center gap-2">
                      🚀 Usar Agente
                      <ArrowRightIcon className="h-5 w-5" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </DashboardLayout>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute>
      <DashboardLayout>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center gap-4">
            <button
              onClick={resetAgent}
              className="text-gray-500 hover:text-gray-700"
            >
              ← Voltar
            </button>
            <div className="flex items-center gap-3">
              <div className={
                currentAgent?.id === 'atendimento' ? 'bg-blue-100 p-2 rounded-lg' :
                  currentAgent?.id === 'planejamento' ? 'bg-green-100 p-2 rounded-lg' :
                    'bg-purple-100 p-2 rounded-lg'
              }>
                {currentAgent?.icon && <currentAgent.icon className={
                  currentAgent?.id === 'atendimento' ? 'h-5 w-5 text-blue-600' :
                    currentAgent?.id === 'planejamento' ? 'h-5 w-5 text-green-600' :
                      'h-5 w-5 text-purple-600'
                } />}
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">{currentAgent?.name}</h1>
                <p className="text-sm text-gray-600">{currentAgent?.description}</p>
              </div>
            </div>
          </div>

          {!result ? (
            <div className="bg-white rounded-lg shadow p-6">
              <form onSubmit={handleSubmit} className="space-y-6">
                <div>
                  <label htmlFor="input" className="block text-sm font-medium text-gray-700 mb-2">
                    Descreva sua demanda
                  </label>
                  <textarea
                    id="input"
                    value={input}
                    onChange={(e) => setInput(e.target.value)}
                    rows={6}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none"
                    placeholder={currentAgent?.inputPlaceholder}
                    required
                  />
                </div>

                <button
                  type="submit"
                  disabled={isProcessing || !input.trim()}
                  className="w-full bg-blue-600 text-white py-4 px-6 rounded-md font-semibold text-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all flex items-center justify-center gap-2 border-2 border-blue-600"
                  style={{ minHeight: '56px' }}
                >
                  {isProcessing ? (
                    <>
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white"></div>
                      ⏳ Processando...
                    </>
                  ) : (
                    <>
                      <SparklesIcon className="h-6 w-6" />
                      🚀 Processar com {currentAgent?.name}
                    </>
                  )}
                </button>
              </form>

              {/* Examples */}
              <div className="mt-8 pt-6 border-t border-gray-200">
                <h3 className="text-sm font-medium text-gray-900 mb-3">💡 Exemplos:</h3>
                <div className="space-y-2">
                  {currentAgent?.examples.map((example, idx) => (
                    <button
                      key={idx}
                      onClick={() => setInput(example)}
                      className="text-left text-sm text-blue-600 hover:text-blue-700 block w-full p-2 rounded hover:bg-blue-50 transition-colors"
                    >
                      💡 {example}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          ) : (
            <div className="space-y-6">
              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center gap-2 mb-4">
                  <DocumentTextIcon className="h-5 w-5 text-green-500" />
                  <h2 className="text-lg font-semibold text-gray-900">Resultado Processado</h2>
                </div>

                <div className="prose max-w-none">
                  <pre className="whitespace-pre-wrap text-sm text-gray-700 font-sans leading-relaxed">
                    {result}
                  </pre>
                </div>
              </div>

              <div className="flex gap-4">
                <button
                  onClick={() => {
                    setResult(null)
                    setInput('')
                  }}
                  className="flex-1 bg-white text-gray-700 py-2 px-4 rounded-md font-medium border border-gray-300 hover:bg-gray-50 transition-colors"
                >
                  Nova Consulta
                </button>
                <button className="flex-1 bg-green-600 text-white py-2 px-4 rounded-md font-medium hover:bg-green-700 transition-colors">
                  💾 Salvar em Campanha
                </button>
              </div>
            </div>
          )}
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  )
}
