'use client'

import { useState } from 'react'
import ProtectedRoute from '@/components/auth/ProtectedRoute'
import DashboardLayout from '@/components/dashboard/DashboardLayout'
import {
  UserGroupIcon,
  CalendarDaysIcon,
  ChartBarIcon,
  SparklesIcon,
  DocumentTextIcon,
  ArrowRightIcon,
  ClipboardDocumentIcon
} from '@heroicons/react/24/outline'
import { generateAgentResponse, analyzeUserInput } from '@/lib/agents/enhancedAgentSystem'
import type { AgentResponse, AdvancedAnalysis } from '@/lib/agents/enhancedAgentSystem'

const agents = [
  {
    id: 'atendimento',
    name: 'Agente de Atendimento',
    description: 'Organiza briefings, acompanha prazos e mantém a comunicação com clientes sempre em dia.',
    icon: UserGroupIcon,
    color: 'blue',
    capabilities: [
      'Estrutura briefings automaticamente',
      'Cria cronogramas de aprovação',
      'Monitora prazos e entregas',
      'Gera relatórios de status'
    ],
    inputPlaceholder: 'Cole aqui o briefing do cliente ou descreva a demanda...',
    examples: [
      'Briefing: Campanha para lançamento de produto X, público jovem 18-25 anos, budget R$ 50k',
      'Cliente solicitou alterações no cronograma da campanha Y',
      'Preciso organizar as aprovações para a campanha de Black Friday'
    ]
  },
  {
    id: 'planejamento',
    name: 'Agente de Planejamento',
    description: 'Gera cronogramas inteligentes, insights estratégicos e organiza toda a execução da campanha.',
    icon: CalendarDaysIcon,
    color: 'green',
    capabilities: [
      'Cronogramas automáticos',
      'Análise de concorrência',
      'Sugestões de estratégia',
      'Distribuição de budget'
    ],
    inputPlaceholder: 'Descreva o projeto ou campanha que precisa ser planejada...',
    examples: [
      'Campanha de 60 dias para lançamento de app, budget R$ 100k, 3 fases',
      'Planejamento estratégico para Black Friday, múltiplos canais',
      'Cronograma para campanha institucional com prazo apertado'
    ]
  },
  {
    id: 'midia',
    name: 'Agente de Mídia',
    description: 'Analisa performance, sugere otimizações e gera relatórios com insights acionáveis.',
    icon: ChartBarIcon,
    color: 'purple',
    capabilities: [
      'Análise de KPIs',
      'Sugestões de otimização',
      'Relatórios automatizados',
      'Alertas de performance'
    ],
    inputPlaceholder: 'Cole dados de performance ou descreva a campanha para análise...',
    examples: [
      'CTR: 2.1%, CPC: R$ 0.85, Budget gasto: R$ 15k de R$ 30k',
      'Campanha Instagram com baixo engajamento, preciso otimizar',
      'Análise de performance da campanha de verão nos últimos 30 dias'
    ]
  }
]

export default function AgentesPage() {
  const [selectedAgent, setSelectedAgent] = useState<string | null>(null)
  const [input, setInput] = useState('')
  const [isProcessing, setIsProcessing] = useState(false)
  const [result, setResult] = useState<string | null>(null)
  const [agentResponse, setAgentResponse] = useState<AgentResponse | null>(null)
  const [inputAnalysis, setInputAnalysis] = useState<AdvancedAnalysis | null>(null)
  const [showAnalysis, setShowAnalysis] = useState(false)
  const [copySuccess, setCopySuccess] = useState(false)

  const currentAgent = agents.find(agent => agent.id === selectedAgent)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!input.trim() || !currentAgent) return

    setIsProcessing(true)
    setResult(null)
    setAgentResponse(null)

    try {
      // Analyze input first for preview
      const analysis = analyzeUserInput(input)
      setInputAnalysis(analysis)

      // Generate enhanced response
      const response = await generateAgentResponse(currentAgent.id, input)
      setAgentResponse(response)
      setResult(response.formattedOutput)
    } catch (error) {
      console.error('Error generating response:', error)
      setResult('Erro ao processar solicitação. Tente novamente.')
    } finally {
      setIsProcessing(false)
    }
  }

  const handleAnalyzeInput = () => {
    if (!input.trim()) return

    const analysis = analyzeUserInput(input)
    setInputAnalysis(analysis)
    setShowAnalysis(true)
  }

  const handleCopyResponse = async () => {
    if (!result) return

    try {
      await navigator.clipboard.writeText(result)
      setCopySuccess(true)
      setTimeout(() => setCopySuccess(false), 2000)
    } catch (error) {
      console.error('Erro ao copiar:', error)
    }
  }

  const resetAgent = () => {
    setSelectedAgent(null)
    setInput('')
    setResult(null)
    setAgentResponse(null)
    setInputAnalysis(null)
    setShowAnalysis(false)
    setCopySuccess(false)
  }

  if (!selectedAgent) {
    return (
      <ProtectedRoute>
        <DashboardLayout>
          <div className="space-y-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Agentes Inteligentes</h1>
              <p className="text-gray-600">Escolha um agente especializado para ajudar com sua tarefa</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {agents.map((agent) => (
                <div key={agent.id} className="bg-white rounded-lg shadow hover:shadow-md transition-shadow cursor-pointer" onClick={() => setSelectedAgent(agent.id)}>
                  <div className="p-6">
                    <div className="flex items-center gap-3 mb-4">
                      <div className={
                        agent.id === 'atendimento' ? 'bg-blue-100 p-3 rounded-lg' :
                          agent.id === 'planejamento' ? 'bg-green-100 p-3 rounded-lg' :
                            'bg-purple-100 p-3 rounded-lg'
                      }>
                        <agent.icon className={
                          agent.id === 'atendimento' ? 'h-6 w-6 text-blue-600' :
                            agent.id === 'planejamento' ? 'h-6 w-6 text-green-600' :
                              'h-6 w-6 text-purple-600'
                        } />
                      </div>
                      <h3 className="font-semibold text-gray-900">{agent.name}</h3>
                    </div>

                    <p className="text-gray-600 mb-4">{agent.description}</p>

                    <div className="space-y-2 mb-6">
                      {agent.capabilities.map((capability, idx) => (
                        <div key={idx} className="flex items-center gap-2">
                          <div className="w-1.5 h-1.5 bg-primary-500 rounded-full"></div>
                          <span className="text-sm text-gray-700">{capability}</span>
                        </div>
                      ))}
                    </div>

                    <button className="w-full bg-blue-600 text-white py-3 px-4 rounded-md font-semibold hover:bg-blue-700 transition-colors flex items-center justify-center gap-2">
                      🚀 Usar Agente
                      <ArrowRightIcon className="h-5 w-5" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </DashboardLayout>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute>
      <DashboardLayout>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center gap-4">
            <button
              onClick={resetAgent}
              className="text-gray-500 hover:text-gray-700"
            >
              ← Voltar
            </button>
            <div className="flex items-center gap-3">
              <div className={
                currentAgent?.id === 'atendimento' ? 'bg-blue-100 p-2 rounded-lg' :
                  currentAgent?.id === 'planejamento' ? 'bg-green-100 p-2 rounded-lg' :
                    'bg-purple-100 p-2 rounded-lg'
              }>
                {currentAgent?.icon && <currentAgent.icon className={
                  currentAgent?.id === 'atendimento' ? 'h-5 w-5 text-blue-600' :
                    currentAgent?.id === 'planejamento' ? 'h-5 w-5 text-green-600' :
                      'h-5 w-5 text-purple-600'
                } />}
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">{currentAgent?.name}</h1>
                <p className="text-sm text-gray-600">{currentAgent?.description}</p>
              </div>
            </div>
          </div>

          {!result ? (
            <div className="bg-white rounded-lg shadow p-6">
              <form onSubmit={handleSubmit} className="space-y-6">
                <div>
                  <label htmlFor="input" className="block text-sm font-medium text-gray-700 mb-2">
                    Descreva sua demanda
                  </label>
                  <textarea
                    id="input"
                    value={input}
                    onChange={(e) => setInput(e.target.value)}
                    rows={6}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none"
                    placeholder={currentAgent?.inputPlaceholder}
                    required
                  />
                </div>

                <div className="flex gap-3">
                  <button
                    type="button"
                    onClick={handleAnalyzeInput}
                    disabled={!input.trim()}
                    className="flex-1 bg-gray-100 text-gray-700 py-3 px-4 rounded-md font-medium hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all flex items-center justify-center gap-2"
                  >
                    🔍 Analisar Input
                  </button>

                  <button
                    type="submit"
                    disabled={isProcessing || !input.trim()}
                    className="flex-2 bg-blue-600 text-white py-3 px-6 rounded-md font-semibold hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all flex items-center justify-center gap-2 border-2 border-blue-600"
                    style={{ minWidth: '200px' }}
                  >
                    {isProcessing ? (
                      <>
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                        ⏳ Processando...
                      </>
                    ) : (
                      <>
                        <SparklesIcon className="h-5 w-5" />
                        🚀 Processar
                      </>
                    )}
                  </button>
                </div>
              </form>

              {/* Examples */}
              <div className="mt-8 pt-6 border-t border-gray-200">
                <h3 className="text-sm font-medium text-gray-900 mb-3">💡 Exemplos:</h3>
                <div className="space-y-2">
                  {currentAgent?.examples.map((example, idx) => (
                    <button
                      key={idx}
                      onClick={() => setInput(example)}
                      className="text-left text-sm text-blue-600 hover:text-blue-700 block w-full p-2 rounded hover:bg-blue-50 transition-colors"
                    >
                      💡 {example}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          ) : (
            <div className="space-y-6">
              {/* Input Analysis Preview */}
              {inputAnalysis && showAnalysis && (
                <div className="bg-blue-50 rounded-lg border border-blue-200 p-4">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="text-sm font-semibold text-blue-900">Análise do Input</h3>
                    <button
                      onClick={() => setShowAnalysis(false)}
                      className="text-blue-600 hover:text-blue-800 text-sm"
                    >
                      Ocultar
                    </button>
                  </div>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-blue-700 font-medium">Complexidade:</span>
                      <span className="ml-1 text-blue-900 capitalize">{inputAnalysis.complexity}</span>
                    </div>
                    <div>
                      <span className="text-blue-700 font-medium">Urgência:</span>
                      <span className="ml-1 text-blue-900 capitalize">{inputAnalysis.urgency}</span>
                    </div>
                    <div>
                      <span className="text-blue-700 font-medium">Completude:</span>
                      <span className="ml-1 text-blue-900">{Math.round(inputAnalysis.completeness * 100)}%</span>
                    </div>
                    <div>
                      <span className="text-blue-700 font-medium">Sentimento:</span>
                      <span className="ml-1 text-blue-900 capitalize">{inputAnalysis.sentiment}</span>
                    </div>
                  </div>
                  {inputAnalysis.riskFactors.length > 0 && (
                    <div className="mt-3 pt-3 border-t border-blue-200">
                      <span className="text-blue-700 font-medium text-sm">Riscos Identificados:</span>
                      <span className="ml-1 text-red-600 text-sm">{inputAnalysis.riskFactors.length} fatores</span>
                    </div>
                  )}
                </div>
              )}

              <div className="bg-white rounded-lg shadow">
                {/* Response Quality Metrics */}
                {agentResponse && (
                  <div className="border-b border-gray-200 p-4">
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="text-sm font-semibold text-gray-900">Métricas de Qualidade</h3>
                      <div className="flex items-center gap-4 text-xs text-gray-600">
                        <span>Processado em {agentResponse.metadata.processingTime}ms</span>
                        <span>Tempo de leitura: {agentResponse.response.estimatedReadTime} min</span>
                      </div>
                    </div>
                    <div className="grid grid-cols-3 gap-4">
                      <div className="text-center">
                        <div className="text-lg font-bold text-green-600">
                          {Math.round(agentResponse.metadata.confidence * 100)}%
                        </div>
                        <div className="text-xs text-gray-600">Confiança</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-bold text-blue-600">
                          {Math.round(agentResponse.metadata.completeness * 100)}%
                        </div>
                        <div className="text-xs text-gray-600">Completude</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-bold text-purple-600">
                          {Math.round(agentResponse.metadata.qualityScore * 100)}%
                        </div>
                        <div className="text-xs text-gray-600">Qualidade</div>
                      </div>
                    </div>
                    {agentResponse.metadata.followUpRecommended && (
                      <div className="mt-3 p-2 bg-yellow-50 border border-yellow-200 rounded text-sm text-yellow-800">
                        💡 Recomendamos uma conversa de acompanhamento para esclarecer detalhes adicionais.
                      </div>
                    )}
                  </div>
                )}

                <div className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-2">
                      <DocumentTextIcon className="h-5 w-5 text-green-500" />
                      <h2 className="text-lg font-semibold text-gray-900">Resultado Processado</h2>
                    </div>
                    <button
                      onClick={handleCopyResponse}
                      className="flex items-center gap-2 px-3 py-2 text-sm bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-md transition-colors"
                    >
                      <ClipboardDocumentIcon className="h-4 w-4" />
                      {copySuccess ? 'Copiado!' : 'Copiar Resposta'}
                    </button>
                  </div>

                  <div className="prose max-w-none">
                    <pre className="whitespace-pre-wrap text-sm text-gray-700 font-sans leading-relaxed">
                      {result}
                    </pre>
                  </div>

                  {/* Action Items */}
                  {agentResponse && agentResponse.response.actionItems.length > 0 && (
                    <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                      <h3 className="text-sm font-semibold text-gray-900 mb-3">Itens de Ação Identificados</h3>
                      <ul className="space-y-2">
                        {agentResponse.response.actionItems.map((item, index) => (
                          <li key={index} className="flex items-start gap-2 text-sm text-gray-700">
                            <span className="text-blue-500 mt-1">•</span>
                            {item}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {/* Follow-up Questions */}
                  {agentResponse && agentResponse.response.followUpQuestions.length > 0 && (
                    <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                      <h3 className="text-sm font-semibold text-blue-900 mb-3">Perguntas para Aprofundamento</h3>
                      <ul className="space-y-2">
                        {agentResponse.response.followUpQuestions.map((question, index) => (
                          <li key={index} className="text-sm text-blue-800">
                            {index + 1}. {question}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              </div>

              <div className="flex gap-4">
                <button
                  onClick={() => {
                    setResult(null)
                    setInput('')
                  }}
                  className="flex-1 bg-white text-gray-700 py-2 px-4 rounded-md font-medium border border-gray-300 hover:bg-gray-50 transition-colors"
                >
                  Nova Consulta
                </button>
                <button className="flex-1 bg-green-600 text-white py-2 px-4 rounded-md font-medium hover:bg-green-700 transition-colors">
                  💾 Salvar em Campanha
                </button>
              </div>
            </div>
          )}
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  )
}
