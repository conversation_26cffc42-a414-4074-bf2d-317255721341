{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent3/agentepub/src/components/auth/ProtectedRoute.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/contexts/AuthContext'\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode\n  redirectTo?: string\n}\n\nexport default function ProtectedRoute({ children, redirectTo = '/' }: ProtectedRouteProps) {\n  const { user, loading } = useAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (!loading && !user) {\n      router.push(redirectTo)\n    }\n  }, [user, loading, router, redirectTo])\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"></div>\n      </div>\n    )\n  }\n\n  if (!user) {\n    return null\n  }\n\n  return <>{children}</>\n}\n\n// Loading component for better UX\nexport function LoadingSpinner({ size = 'md' }: { size?: 'sm' | 'md' | 'lg' }) {\n  const sizeClasses = {\n    sm: 'h-4 w-4',\n    md: 'h-8 w-8',\n    lg: 'h-12 w-12'\n  }\n\n  return (\n    <div className=\"flex items-center justify-center\">\n      <div className={`animate-spin rounded-full border-b-2 border-primary-600 ${sizeClasses[size]}`}></div>\n    </div>\n  )\n}\n\n// Auth guard hook for pages\nexport function useAuthGuard(redirectTo = '/') {\n  const { user, loading } = useAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (!loading && !user) {\n      router.push(redirectTo)\n    }\n  }, [user, loading, router, redirectTo])\n\n  return { user, loading, isAuthenticated: !!user }\n}\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AACA;;;AAJA;;;;AAWe,SAAS,eAAe,KAAmD;QAAnD,EAAE,QAAQ,EAAE,aAAa,GAAG,EAAuB,GAAnD;;IACrC,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,IAAA,6IAAO;IACjC,MAAM,SAAS,IAAA,kJAAS;IAExB,IAAA,0KAAS;oCAAC;YACR,IAAI,CAAC,WAAW,CAAC,MAAM;gBACrB,OAAO,IAAI,CAAC;YACd;QACF;mCAAG;QAAC;QAAM;QAAS;QAAQ;KAAW;IAEtC,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBAAO;kBAAG;;AACZ;GAvBwB;;QACI,6IAAO;QAClB,kJAAS;;;KAFF;AA0BjB,SAAS,eAAe,KAA8C;QAA9C,EAAE,OAAO,IAAI,EAAiC,GAA9C;IAC7B,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAW,AAAC,2DAA4E,OAAlB,WAAW,CAAC,KAAK;;;;;;;;;;;AAGlG;MAZgB;AAeT,SAAS;QAAa,aAAA,iEAAa;;IACxC,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,IAAA,6IAAO;IACjC,MAAM,SAAS,IAAA,kJAAS;IAExB,IAAA,0KAAS;kCAAC;YACR,IAAI,CAAC,WAAW,CAAC,MAAM;gBACrB,OAAO,IAAI,CAAC;YACd;QACF;iCAAG;QAAC;QAAM;QAAS;QAAQ;KAAW;IAEtC,OAAO;QAAE;QAAM;QAAS,iBAAiB,CAAC,CAAC;IAAK;AAClD;IAXgB;;QACY,6IAAO;QAClB,kJAAS", "debugId": null}}, {"offset": {"line": 131, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent3/agentepub/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatDate(date: string | Date) {\n  return new Intl.DateTimeFormat('pt-BR', {\n    day: '2-digit',\n    month: '2-digit',\n    year: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(new Date(date))\n}\n\nexport function formatCurrency(amount: number) {\n  return new Intl.NumberFormat('pt-BR', {\n    style: 'currency',\n    currency: 'BRL',\n  }).format(amount)\n}\n\nexport function getAreaDisplayName(area: string) {\n  const areaNames = {\n    atendimento: 'Atendimento',\n    planejamento: 'Planejamento',\n    midia: 'Mídia'\n  }\n  return areaNames[area as keyof typeof areaNames] || area\n}\n\nexport function generateSlug(text: string) {\n  return text\n    .toLowerCase()\n    .normalize('NFD')\n    .replace(/[\\u0300-\\u036f]/g, '')\n    .replace(/[^a-z0-9\\s-]/g, '')\n    .replace(/\\s+/g, '-')\n    .replace(/-+/g, '-')\n    .trim()\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,IAAA,yKAAO,EAAC,IAAA,gJAAI,EAAC;AACtB;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,KAAK;QACL,OAAO;QACP,MAAM;QACN,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAEO,SAAS,eAAe,MAAc;IAC3C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,mBAAmB,IAAY;IAC7C,MAAM,YAAY;QAChB,aAAa;QACb,cAAc;QACd,OAAO;IACT;IACA,OAAO,SAAS,CAAC,KAA+B,IAAI;AACtD;AAEO,SAAS,aAAa,IAAY;IACvC,OAAO,KACJ,WAAW,GACX,SAAS,CAAC,OACV,OAAO,CAAC,oBAAoB,IAC5B,OAAO,CAAC,iBAAiB,IACzB,OAAO,CAAC,QAAQ,KAChB,OAAO,CAAC,OAAO,KACf,IAAI;AACT", "debugId": null}}, {"offset": {"line": 186, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent3/agentepub/src/components/dashboard/DashboardLayout.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { cn } from '@/lib/utils'\nimport {\n  Bars3Icon,\n  XMarkIcon,\n  HomeIcon,\n  FolderIcon,\n  UserGroupIcon,\n  DocumentChartBarIcon,\n  ChatBubbleLeftRightIcon,\n  Cog6ToothIcon,\n  ArrowRightOnRectangleIcon\n} from '@heroicons/react/24/outline'\n\nconst navigation = [\n  { name: 'Dashboard', href: '/dashboard', icon: HomeIcon },\n  { name: 'Campanhas', href: '/dashboard/campanhas', icon: FolderIcon },\n  { name: 'Agent<PERSON>', href: '/dashboard/agentes', icon: UserGroupIcon },\n  { name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', href: '/dashboard/relatorios', icon: DocumentChartBarIcon },\n  { name: 'Chat', href: '/dashboard/chat', icon: ChatBubbleLeftRightIcon },\n  { name: 'Configura<PERSON>õ<PERSON>', href: '/dashboard/configuracoes', icon: Cog6ToothIcon },\n]\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode\n}\n\nexport default function DashboardLayout({ children }: DashboardLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false)\n  const pathname = usePathname()\n  const { user, profile, signOut } = useAuth()\n\n  const handleSignOut = async () => {\n    await signOut()\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Mobile sidebar */}\n      {sidebarOpen && (\n        <div className=\"fixed inset-0 z-50 lg:hidden\">\n          <div className=\"fixed inset-0 bg-gray-600 bg-opacity-75\" onClick={() => setSidebarOpen(false)} />\n          <div className=\"fixed inset-y-0 left-0 flex w-full max-w-xs flex-col bg-white shadow-xl\">\n            <div className=\"flex h-16 items-center justify-between px-6 border-b border-gray-200\">\n              <Link href=\"/dashboard\" className=\"text-xl font-bold text-primary-600\">\n                AgentePub\n              </Link>\n              <button\n                onClick={() => setSidebarOpen(false)}\n                className=\"text-gray-400 hover:text-gray-600\"\n              >\n                <XMarkIcon className=\"h-6 w-6\" />\n              </button>\n            </div>\n            \n            <nav className=\"flex-1 px-6 py-6\">\n              <ul className=\"space-y-2\">\n                {navigation.map((item) => (\n                  <li key={item.name}>\n                    <Link\n                      href={item.href}\n                      className={cn(\n                        'flex items-center gap-3 px-3 py-2 rounded-md text-sm font-medium transition-colors',\n                        pathname === item.href\n                          ? 'bg-primary-100 text-primary-700'\n                          : 'text-gray-700 hover:bg-gray-100'\n                      )}\n                      onClick={() => setSidebarOpen(false)}\n                    >\n                      <item.icon className=\"h-5 w-5\" />\n                      {item.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </nav>\n\n            <div className=\"border-t border-gray-200 p-6\">\n              <div className=\"flex items-center gap-3 mb-4\">\n                <div className=\"h-8 w-8 bg-primary-100 rounded-full flex items-center justify-center\">\n                  <span className=\"text-sm font-medium text-primary-700\">\n                    {profile?.full_name?.charAt(0) || user?.email?.charAt(0) || 'U'}\n                  </span>\n                </div>\n                <div className=\"flex-1 min-w-0\">\n                  <p className=\"text-sm font-medium text-gray-900 truncate\">\n                    {profile?.full_name || user?.email}\n                  </p>\n                  {profile?.area && (\n                    <p className=\"text-xs text-gray-500 capitalize\">{profile.area}</p>\n                  )}\n                </div>\n              </div>\n              <button\n                onClick={handleSignOut}\n                className=\"flex items-center gap-2 text-sm text-gray-700 hover:text-gray-900 transition-colors\"\n              >\n                <ArrowRightOnRectangleIcon className=\"h-4 w-4\" />\n                Sair\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col\">\n        <div className=\"flex flex-col flex-1 bg-white border-r border-gray-200\">\n          <div className=\"flex h-16 items-center px-6 border-b border-gray-200\">\n            <Link href=\"/dashboard\" className=\"text-xl font-bold text-primary-600\">\n              AgentePub\n            </Link>\n          </div>\n          \n          <nav className=\"flex-1 px-6 py-6\">\n            <ul className=\"space-y-2\">\n              {navigation.map((item) => (\n                <li key={item.name}>\n                  <Link\n                    href={item.href}\n                    className={cn(\n                      'flex items-center gap-3 px-3 py-2 rounded-md text-sm font-medium transition-colors',\n                      pathname === item.href\n                        ? 'bg-primary-100 text-primary-700'\n                        : 'text-gray-700 hover:bg-gray-100'\n                    )}\n                  >\n                    <item.icon className=\"h-5 w-5\" />\n                    {item.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </nav>\n\n          <div className=\"border-t border-gray-200 p-6\">\n            <div className=\"flex items-center gap-3 mb-4\">\n              <div className=\"h-8 w-8 bg-primary-100 rounded-full flex items-center justify-center\">\n                <span className=\"text-sm font-medium text-primary-700\">\n                  {profile?.full_name?.charAt(0) || user?.email?.charAt(0) || 'U'}\n                </span>\n              </div>\n              <div className=\"flex-1 min-w-0\">\n                <p className=\"text-sm font-medium text-gray-900 truncate\">\n                  {profile?.full_name || user?.email}\n                </p>\n                {profile?.area && (\n                  <p className=\"text-xs text-gray-500 capitalize\">{profile.area}</p>\n                )}\n              </div>\n            </div>\n            <button\n              onClick={handleSignOut}\n              className=\"flex items-center gap-2 text-sm text-gray-700 hover:text-gray-900 transition-colors\"\n            >\n              <ArrowRightOnRectangleIcon className=\"h-4 w-4\" />\n              Sair\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"lg:pl-64\">\n        {/* Top bar */}\n        <div className=\"sticky top-0 z-40 bg-white border-b border-gray-200 px-6 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <button\n              onClick={() => setSidebarOpen(true)}\n              className=\"lg:hidden text-gray-500 hover:text-gray-700\"\n            >\n              <Bars3Icon className=\"h-6 w-6\" />\n            </button>\n            \n            <div className=\"flex items-center gap-4\">\n              <div className=\"text-sm text-gray-500\">\n                {new Date().toLocaleDateString('pt-BR', { \n                  weekday: 'long', \n                  year: 'numeric', \n                  month: 'long', \n                  day: 'numeric' \n                })}\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"px-6 py-8\">\n          {children}\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAPA;;;;;;;AAmBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,8NAAQ;IAAC;IACxD;QAAE,MAAM;QAAa,MAAM;QAAwB,MAAM,oOAAU;IAAC;IACpE;QAAE,MAAM;QAAW,MAAM;QAAsB,MAAM,6OAAa;IAAC;IACnE;QAAE,MAAM;QAAc,MAAM;QAAyB,MAAM,kQAAoB;IAAC;IAChF;QAAE,MAAM;QAAQ,MAAM;QAAmB,MAAM,2QAAuB;IAAC;IACvE;QAAE,MAAM;QAAiB,MAAM;QAA4B,MAAM,6OAAa;IAAC;CAChF;AAMc,SAAS,gBAAgB,KAAkC;QAAlC,EAAE,QAAQ,EAAwB,GAAlC;QAsDnB,oBAAiC,aA0DnC,qBAAiC;;IA/GlD,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,yKAAQ,EAAC;IAC/C,MAAM,WAAW,IAAA,oJAAW;IAC5B,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,IAAA,6IAAO;IAE1C,MAAM,gBAAgB;QACpB,MAAM;IACR;IAEA,qBACE,6LAAC;QAAI,WAAU;;YAEZ,6BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;wBAA0C,SAAS,IAAM,eAAe;;;;;;kCACvF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,0KAAI;wCAAC,MAAK;wCAAa,WAAU;kDAAqC;;;;;;kDAGvE,6LAAC;wCACC,SAAS,IAAM,eAAe;wCAC9B,WAAU;kDAEV,cAAA,6LAAC,iOAAS;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAIzB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAG,WAAU;8CACX,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;sDACC,cAAA,6LAAC,0KAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAW,IAAA,4HAAE,EACX,sFACA,aAAa,KAAK,IAAI,GAClB,oCACA;gDAEN,SAAS,IAAM,eAAe;;kEAE9B,6LAAC,KAAK,IAAI;wDAAC,WAAU;;;;;;oDACpB,KAAK,IAAI;;;;;;;2CAZL,KAAK,IAAI;;;;;;;;;;;;;;;0CAmBxB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DACb,CAAA,oBAAA,+BAAA,qBAAA,QAAS,SAAS,cAAlB,yCAAA,mBAAoB,MAAM,CAAC,QAAM,iBAAA,4BAAA,cAAA,KAAM,KAAK,cAAX,kCAAA,YAAa,MAAM,CAAC,OAAM;;;;;;;;;;;0DAGhE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEACV,CAAA,oBAAA,8BAAA,QAAS,SAAS,MAAI,iBAAA,2BAAA,KAAM,KAAK;;;;;;oDAEnC,CAAA,oBAAA,8BAAA,QAAS,IAAI,mBACZ,6LAAC;wDAAE,WAAU;kEAAoC,QAAQ,IAAI;;;;;;;;;;;;;;;;;;kDAInE,6LAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,6LAAC,iRAAyB;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;;;;;;;;;;;;;0BAS3D,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,0KAAI;gCAAC,MAAK;gCAAa,WAAU;0CAAqC;;;;;;;;;;;sCAKzE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAG,WAAU;0CACX,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;kDACC,cAAA,6LAAC,0KAAI;4CACH,MAAM,KAAK,IAAI;4CACf,WAAW,IAAA,4HAAE,EACX,sFACA,aAAa,KAAK,IAAI,GAClB,oCACA;;8DAGN,6LAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;gDACpB,KAAK,IAAI;;;;;;;uCAXL,KAAK,IAAI;;;;;;;;;;;;;;;sCAkBxB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DACb,CAAA,oBAAA,+BAAA,sBAAA,QAAS,SAAS,cAAlB,0CAAA,oBAAoB,MAAM,CAAC,QAAM,iBAAA,4BAAA,eAAA,KAAM,KAAK,cAAX,mCAAA,aAAa,MAAM,CAAC,OAAM;;;;;;;;;;;sDAGhE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DACV,CAAA,oBAAA,8BAAA,QAAS,SAAS,MAAI,iBAAA,2BAAA,KAAM,KAAK;;;;;;gDAEnC,CAAA,oBAAA,8BAAA,QAAS,IAAI,mBACZ,6LAAC;oDAAE,WAAU;8DAAoC,QAAQ,IAAI;;;;;;;;;;;;;;;;;;8CAInE,6LAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,6LAAC,iRAAyB;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;;;;;;;;;;;;;0BAQzD,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CAEV,cAAA,6LAAC,iOAAS;wCAAC,WAAU;;;;;;;;;;;8CAGvB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACZ,IAAI,OAAO,kBAAkB,CAAC,SAAS;4CACtC,SAAS;4CACT,MAAM;4CACN,OAAO;4CACP,KAAK;wCACP;;;;;;;;;;;;;;;;;;;;;;kCAOR,6LAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;;;;;;;AAKX;GAvKwB;;QAEL,oJAAW;QACO,6IAAO;;;KAHpB", "debugId": null}}, {"offset": {"line": 670, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent3/agentepub/src/lib/agents/responseEngine.ts"], "sourcesContent": ["/**\n * Enhanced Response Generation Engine\n *\n * This module provides sophisticated response generation capabilities for AI agents,\n * transforming basic inputs into comprehensive, detailed, and contextually relevant responses.\n */\n\nexport interface InputAnalysis {\n  keywords: string[]\n  entities: string[]\n  sentiment: 'positive' | 'neutral' | 'negative'\n  complexity: 'basic' | 'intermediate' | 'advanced'\n  intent: string\n  context: Record<string, any>\n  urgency: 'low' | 'medium' | 'high'\n  completeness: number // 0-1 score\n}\n\nexport interface ResponseSection {\n  title: string\n  content: string\n  type: 'analysis' | 'recommendation' | 'example' | 'warning' | 'next-steps' | 'best-practices'\n  priority: number\n  metadata?: Record<string, any>\n}\n\nexport interface EnhancedResponse {\n  summary: string\n  sections: ResponseSection[]\n  confidence: number\n  completeness: number\n  followUpQuestions: string[]\n  relatedTopics: string[]\n  estimatedReadTime: number\n  actionItems: string[]\n}\n\nexport class ResponseEngine {\n  private static instance: ResponseEngine\n\n  private constructor() { }\n\n  public static getInstance(): ResponseEngine {\n    if (!ResponseEngine.instance) {\n      ResponseEngine.instance = new ResponseEngine()\n    }\n    return ResponseEngine.instance\n  }\n\n  /**\n   * Analyzes user input to extract meaningful information and context\n   */\n  public analyzeInput(input: string): InputAnalysis {\n    const words = input.toLowerCase().split(/\\s+/)\n    const sentences = input.split(/[.!?]+/).filter(s => s.trim().length > 0)\n\n    // Extract keywords (simplified - in production would use NLP)\n    const keywords = this.extractKeywords(input)\n\n    // Extract entities (clients, dates, budgets, etc.)\n    const entities = this.extractEntities(input)\n\n    // Determine sentiment\n    const sentiment = this.analyzeSentiment(input)\n\n    // Assess complexity based on length, technical terms, and structure\n    const complexity = this.assessComplexity(input, keywords)\n\n    // Determine intent\n    const intent = this.determineIntent(input, keywords)\n\n    // Extract context\n    const context = this.extractContext(input, entities)\n\n    // Assess urgency\n    const urgency = this.assessUrgency(input)\n\n    // Calculate completeness\n    const completeness = this.calculateCompleteness(input, entities)\n\n    return {\n      keywords,\n      entities,\n      sentiment,\n      complexity,\n      intent,\n      context,\n      urgency,\n      completeness\n    }\n  }\n\n  /**\n   * Generates enhanced response based on analysis and agent type\n   */\n  public generateResponse(\n    agentId: string,\n    input: string,\n    analysis: InputAnalysis\n  ): EnhancedResponse {\n    const sections: ResponseSection[] = []\n\n    // Generate core analysis section\n    sections.push(this.generateAnalysisSection(analysis, input))\n\n    // Generate agent-specific sections\n    sections.push(...this.generateAgentSpecificSections(agentId, analysis, input))\n\n    // Generate recommendations\n    sections.push(this.generateRecommendationsSection(agentId, analysis))\n\n    // Generate examples if appropriate\n    if (analysis.complexity !== 'basic') {\n      sections.push(this.generateExamplesSection(agentId, analysis))\n    }\n\n    // Generate warnings and considerations\n    sections.push(this.generateWarningsSection(agentId, analysis))\n\n    // Generate next steps\n    sections.push(this.generateNextStepsSection(agentId, analysis))\n\n    // Generate best practices\n    sections.push(this.generateBestPracticesSection(agentId, analysis))\n\n    // Sort sections by priority\n    sections.sort((a, b) => b.priority - a.priority)\n\n    // Generate summary\n    const summary = this.generateSummary(sections, analysis)\n\n    // Generate follow-up questions\n    const followUpQuestions = this.generateFollowUpQuestions(agentId, analysis)\n\n    // Generate related topics\n    const relatedTopics = this.generateRelatedTopics(agentId, analysis)\n\n    // Generate action items\n    const actionItems = this.extractActionItems(sections)\n\n    // Calculate metrics\n    const confidence = this.calculateConfidence(analysis, sections)\n    const completeness = this.calculateResponseCompleteness(sections, analysis)\n    const estimatedReadTime = this.calculateReadTime(sections)\n\n    return {\n      summary,\n      sections,\n      confidence,\n      completeness,\n      followUpQuestions,\n      relatedTopics,\n      estimatedReadTime,\n      actionItems\n    }\n  }\n\n  private extractKeywords(input: string): string[] {\n    const commonWords = new Set(['o', 'a', 'de', 'para', 'com', 'em', 'um', 'uma', 'do', 'da', 'no', 'na', 'por', 'se', 'que', 'como', 'mais', 'mas', 'ou', 'e', 'é', 'são', 'foi', 'ser', 'ter', 'seu', 'sua', 'seus', 'suas'])\n\n    const words = input.toLowerCase()\n      .replace(/[^\\w\\s]/g, ' ')\n      .split(/\\s+/)\n      .filter(word => word.length > 2 && !commonWords.has(word))\n\n    // Count frequency and return most common\n    const frequency: Record<string, number> = {}\n    words.forEach(word => {\n      frequency[word] = (frequency[word] || 0) + 1\n    })\n\n    return Object.entries(frequency)\n      .sort(([, a], [, b]) => b - a)\n      .slice(0, 10)\n      .map(([word]) => word)\n  }\n\n  private extractEntities(input: string): string[] {\n    const entities: string[] = []\n\n    // Extract monetary values\n    const moneyRegex = /R\\$\\s*[\\d.,]+|[\\d.,]+\\s*reais?/gi\n    const moneyMatches = input.match(moneyRegex)\n    if (moneyMatches) entities.push(...moneyMatches)\n\n    // Extract dates\n    const dateRegex = /\\d{1,2}\\/\\d{1,2}\\/\\d{4}|\\d{1,2}\\s+de\\s+\\w+|\\w+\\s+\\d{1,2}/gi\n    const dateMatches = input.match(dateRegex)\n    if (dateMatches) entities.push(...dateMatches)\n\n    // Extract percentages\n    const percentRegex = /\\d+%/g\n    const percentMatches = input.match(percentRegex)\n    if (percentMatches) entities.push(...percentMatches)\n\n    // Extract company/brand names (capitalized words)\n    const brandRegex = /\\b[A-Z][a-z]+(?:\\s+[A-Z][a-z]+)*\\b/g\n    const brandMatches = input.match(brandRegex)\n    if (brandMatches) entities.push(...brandMatches.slice(0, 5)) // Limit to avoid noise\n\n    return [...new Set(entities)] // Remove duplicates\n  }\n\n  private analyzeSentiment(input: string): 'positive' | 'neutral' | 'negative' {\n    const positiveWords = ['bom', 'ótimo', 'excelente', 'perfeito', 'sucesso', 'aprovado', 'satisfeito', 'feliz', 'positivo']\n    const negativeWords = ['ruim', 'péssimo', 'problema', 'erro', 'falha', 'rejeitado', 'insatisfeito', 'preocupado', 'urgente', 'crítico']\n\n    const words = input.toLowerCase().split(/\\s+/)\n    let positiveScore = 0\n    let negativeScore = 0\n\n    words.forEach(word => {\n      if (positiveWords.some(pw => word.includes(pw))) positiveScore++\n      if (negativeWords.some(nw => word.includes(nw))) negativeScore++\n    })\n\n    if (positiveScore > negativeScore) return 'positive'\n    if (negativeScore > positiveScore) return 'negative'\n    return 'neutral'\n  }\n\n  private assessComplexity(input: string, keywords: string[]): 'basic' | 'intermediate' | 'advanced' {\n    const technicalTerms = ['roi', 'ctr', 'cpm', 'cpc', 'kpi', 'segmentação', 'targeting', 'remarketing', 'lookalike', 'funil', 'conversão']\n    const complexityIndicators = keywords.filter(k => technicalTerms.includes(k.toLowerCase())).length\n\n    if (input.length < 100 && complexityIndicators === 0) return 'basic'\n    if (input.length > 300 || complexityIndicators > 2) return 'advanced'\n    return 'intermediate'\n  }\n\n  private determineIntent(input: string, keywords: string[]): string {\n    const intentPatterns = {\n      'create_briefing': ['briefing', 'criar', 'novo', 'campanha'],\n      'analyze_performance': ['análise', 'performance', 'resultado', 'métricas'],\n      'plan_campaign': ['planejar', 'cronograma', 'estratégia', 'planejamento'],\n      'optimize': ['otimizar', 'melhorar', 'ajustar', 'otimização'],\n      'report': ['relatório', 'report', 'resumo', 'status'],\n      'troubleshoot': ['problema', 'erro', 'não funciona', 'ajuda']\n    }\n\n    const inputLower = input.toLowerCase()\n    for (const [intent, patterns] of Object.entries(intentPatterns)) {\n      if (patterns.some(pattern => inputLower.includes(pattern))) {\n        return intent\n      }\n    }\n\n    return 'general_inquiry'\n  }\n\n  private extractContext(input: string, entities: string[]): Record<string, any> {\n    const context: Record<string, any> = {}\n\n    // Extract budget information\n    const budgetMatch = input.match(/(?:budget|orçamento).*?R\\$\\s*([\\d.,]+)/i)\n    if (budgetMatch) context.budget = budgetMatch[1]\n\n    // Extract timeline information\n    const timelineMatch = input.match(/(\\d+)\\s*(?:dias?|semanas?|meses?)/i)\n    if (timelineMatch) context.timeline = timelineMatch[0]\n\n    // Extract target audience\n    const audienceMatch = input.match(/(?:público|target|audiência).*?(\\d+[-–]\\d+\\s*anos?)/i)\n    if (audienceMatch) context.targetAge = audienceMatch[1]\n\n    // Extract channels\n    const channels = ['instagram', 'facebook', 'google', 'youtube', 'linkedin', 'tiktok']\n    const mentionedChannels = channels.filter(channel =>\n      input.toLowerCase().includes(channel)\n    )\n    if (mentionedChannels.length > 0) context.channels = mentionedChannels\n\n    return context\n  }\n\n  private assessUrgency(input: string): 'low' | 'medium' | 'high' {\n    const urgentWords = ['urgente', 'imediato', 'hoje', 'agora', 'crítico', 'emergência']\n    const mediumWords = ['breve', 'logo', 'próximo', 'semana']\n\n    const inputLower = input.toLowerCase()\n\n    if (urgentWords.some(word => inputLower.includes(word))) return 'high'\n    if (mediumWords.some(word => inputLower.includes(word))) return 'medium'\n    return 'low'\n  }\n\n  private calculateCompleteness(input: string, entities: string[]): number {\n    let score = 0\n    const maxScore = 10\n\n    // Length factor\n    if (input.length > 50) score += 2\n    if (input.length > 150) score += 2\n\n    // Entity factor\n    score += Math.min(entities.length, 3)\n\n    // Structure factor\n    if (input.includes('\\n') || input.includes('-') || input.includes('•')) score += 1\n\n    // Question factor\n    if (input.includes('?')) score += 1\n\n    // Context factor\n    if (input.toLowerCase().includes('cliente') || input.toLowerCase().includes('campanha')) score += 1\n\n    return Math.min(score / maxScore, 1)\n  }\n\n  // Additional helper methods will be implemented in the next part...\n  private generateAnalysisSection(analysis: InputAnalysis, input: string): ResponseSection {\n    return {\n      title: \"📊 Análise do Input\",\n      content: `**Complexidade:** ${analysis.complexity}\\n**Intenção:** ${analysis.intent}\\n**Urgência:** ${analysis.urgency}\\n**Completude:** ${Math.round(analysis.completeness * 100)}%`,\n      type: 'analysis',\n      priority: 10\n    }\n  }\n\n  private generateAgentSpecificSections(agentId: string, analysis: InputAnalysis, input: string): ResponseSection[] {\n    // This will be expanded with agent-specific logic\n    return []\n  }\n\n  private generateRecommendationsSection(agentId: string, analysis: InputAnalysis): ResponseSection {\n    return {\n      title: \"💡 Recomendações\",\n      content: \"Recomendações baseadas na análise...\",\n      type: 'recommendation',\n      priority: 9\n    }\n  }\n\n  private generateExamplesSection(agentId: string, analysis: InputAnalysis): ResponseSection {\n    return {\n      title: \"📝 Exemplos\",\n      content: \"Exemplos práticos...\",\n      type: 'example',\n      priority: 7\n    }\n  }\n\n  private generateWarningsSection(agentId: string, analysis: InputAnalysis): ResponseSection {\n    return {\n      title: \"⚠️ Considerações\",\n      content: \"Pontos de atenção...\",\n      type: 'warning',\n      priority: 8\n    }\n  }\n\n  private generateNextStepsSection(agentId: string, analysis: InputAnalysis): ResponseSection {\n    return {\n      title: \"🎯 Próximos Passos\",\n      content: \"Ações recomendadas...\",\n      type: 'next-steps',\n      priority: 9\n    }\n  }\n\n  private generateBestPracticesSection(agentId: string, analysis: InputAnalysis): ResponseSection {\n    return {\n      title: \"✅ Melhores Práticas\",\n      content: \"Práticas recomendadas...\",\n      type: 'best-practices',\n      priority: 6\n    }\n  }\n\n  private generateSummary(sections: ResponseSection[], analysis: InputAnalysis): string {\n    const mainInsights: string[] = []\n\n    // Extract key insights from analysis\n    if (analysis.complexity === 'advanced') {\n      mainInsights.push('Projeto de alta complexidade identificado')\n    }\n\n    if (analysis.urgency === 'high') {\n      mainInsights.push('Demanda urgente requer atenção imediata')\n    }\n\n    if (analysis.completeness < 0.6) {\n      mainInsights.push('Briefing necessita informações adicionais')\n    }\n\n    if (analysis.context.budget) {\n      const budgetValue = parseFloat(analysis.context.budget.replace(/[^\\d]/g, ''))\n      if (budgetValue > 100000) {\n        mainInsights.push('Budget robusto permite estratégia abrangente')\n      } else if (budgetValue < 10000) {\n        mainInsights.push('Budget limitado requer foco estratégico')\n      }\n    }\n\n    if (analysis.riskFactors && analysis.riskFactors.length > 0) {\n      mainInsights.push(`${analysis.riskFactors.length} fatores de risco identificados`)\n    }\n\n    if (analysis.opportunityIndicators && analysis.opportunityIndicators.length > 0) {\n      mainInsights.push(`${analysis.opportunityIndicators.length} oportunidades detectadas`)\n    }\n\n    // Generate summary based on insights\n    let summary = 'Análise completa do briefing realizada'\n\n    if (mainInsights.length > 0) {\n      summary += ': ' + mainInsights.join(', ').toLowerCase()\n    }\n\n    summary += `. ${sections.length} seções de recomendações geradas com foco em execução prática e resultados mensuráveis.`\n\n    return summary\n  }\n\n  private generateFollowUpQuestions(agentId: string, analysis: InputAnalysis): string[] {\n    const questions: string[] = []\n\n    // Questions based on missing information\n    if (!analysis.context.budget) {\n      questions.push('Qual o orçamento disponível para este projeto?')\n    }\n\n    if (!analysis.context.timeline) {\n      questions.push('Qual o prazo desejado para conclusão?')\n    }\n\n    if (!analysis.context.targetAge && !analysis.keywords.includes('público')) {\n      questions.push('Quem é o público-alvo principal desta campanha?')\n    }\n\n    if (!analysis.context.channels) {\n      questions.push('Quais canais de mídia são preferenciais?')\n    }\n\n    // Agent-specific questions\n    if (agentId === 'atendimento') {\n      if (analysis.completeness < 0.7) {\n        questions.push('Há alguma restrição ou requisito específico do cliente?')\n      }\n      questions.push('Qual o processo de aprovação interno do cliente?')\n    } else if (agentId === 'planejamento') {\n      questions.push('Existem campanhas anteriores que possam servir de benchmark?')\n      questions.push('Quais são os principais concorrentes a serem considerados?')\n    } else if (agentId === 'midia') {\n      questions.push('Quais métricas são mais importantes para o sucesso?')\n      questions.push('Há histórico de performance em canais similares?')\n    }\n\n    return questions.slice(0, 4) // Limit to 4 questions\n  }\n\n  private generateRelatedTopics(agentId: string, analysis: InputAnalysis): string[] {\n    const topics: string[] = []\n\n    // Topics based on keywords and context\n    if (analysis.keywords.includes('campanha')) {\n      topics.push('Estratégia de Campanhas', 'Gestão de Budget', 'Métricas de Performance')\n    }\n\n    if (analysis.keywords.includes('digital') || analysis.context.channels) {\n      topics.push('Marketing Digital', 'Attribution Modeling', 'Otimização de Conversão')\n    }\n\n    if (analysis.context.budget) {\n      topics.push('ROI e Performance', 'Distribuição de Investimento')\n    }\n\n    // Agent-specific topics\n    if (agentId === 'atendimento') {\n      topics.push('Gestão de Cliente', 'Processos de Aprovação', 'Comunicação Estratégica')\n    } else if (agentId === 'planejamento') {\n      topics.push('Planejamento Estratégico', 'Análise de Mercado', 'Cronogramas de Projeto')\n    } else if (agentId === 'midia') {\n      topics.push('Otimização de Mídia', 'Analytics e Tracking', 'Testes A/B')\n    }\n\n    return [...new Set(topics)].slice(0, 5) // Remove duplicates and limit to 5\n  }\n\n  private extractActionItems(sections: ResponseSection[]): string[] {\n    const actionItems: string[] = []\n\n    // Extract action items from sections content\n    sections.forEach(section => {\n      if (section.type === 'next-steps' || section.type === 'recommendation') {\n        const lines = section.content.split('\\n')\n        lines.forEach(line => {\n          // Look for bullet points or numbered items that sound like actions\n          if (line.match(/^[•\\-\\d\\.]\\s*/) &&\n            (line.includes('definir') || line.includes('criar') || line.includes('implementar') ||\n              line.includes('validar') || line.includes('estabelecer') || line.includes('configurar'))) {\n            const cleanLine = line.replace(/^[•\\-\\d\\.\\s]*/, '').trim()\n            if (cleanLine.length > 10) {\n              actionItems.push(cleanLine)\n            }\n          }\n        })\n      }\n    })\n\n    // Add default action items if none found\n    if (actionItems.length === 0) {\n      actionItems.push(\n        'Validar briefing com cliente',\n        'Definir cronograma detalhado',\n        'Estabelecer marcos de aprovação',\n        'Iniciar desenvolvimento da estratégia'\n      )\n    }\n\n    return actionItems.slice(0, 6) // Limit to 6 action items\n  }\n\n  private calculateConfidence(analysis: InputAnalysis, sections: ResponseSection[]): number {\n    return 0.85 // Placeholder\n  }\n\n  private calculateResponseCompleteness(sections: ResponseSection[], analysis: InputAnalysis): number {\n    return 0.90 // Placeholder\n  }\n\n  private calculateReadTime(sections: ResponseSection[]): number {\n    const totalWords = sections.reduce((acc, section) => acc + section.content.split(' ').length, 0)\n    return Math.ceil(totalWords / 200) // Assuming 200 words per minute\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;;;AAgCM,MAAM;IAKX,OAAc,cAA8B;QAC1C,IAAI,CAAC,eAAe,QAAQ,EAAE;YAC5B,eAAe,QAAQ,GAAG,IAAI;QAChC;QACA,OAAO,eAAe,QAAQ;IAChC;IAEA;;GAEC,GACD,AAAO,aAAa,KAAa,EAAiB;QAChD,MAAM,QAAQ,MAAM,WAAW,GAAG,KAAK,CAAC;QACxC,MAAM,YAAY,MAAM,KAAK,CAAC,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,GAAG,MAAM,GAAG;QAEtE,8DAA8D;QAC9D,MAAM,WAAW,IAAI,CAAC,eAAe,CAAC;QAEtC,mDAAmD;QACnD,MAAM,WAAW,IAAI,CAAC,eAAe,CAAC;QAEtC,sBAAsB;QACtB,MAAM,YAAY,IAAI,CAAC,gBAAgB,CAAC;QAExC,oEAAoE;QACpE,MAAM,aAAa,IAAI,CAAC,gBAAgB,CAAC,OAAO;QAEhD,mBAAmB;QACnB,MAAM,SAAS,IAAI,CAAC,eAAe,CAAC,OAAO;QAE3C,kBAAkB;QAClB,MAAM,UAAU,IAAI,CAAC,cAAc,CAAC,OAAO;QAE3C,iBAAiB;QACjB,MAAM,UAAU,IAAI,CAAC,aAAa,CAAC;QAEnC,yBAAyB;QACzB,MAAM,eAAe,IAAI,CAAC,qBAAqB,CAAC,OAAO;QAEvD,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACF;IACF;IAEA;;GAEC,GACD,AAAO,iBACL,OAAe,EACf,KAAa,EACb,QAAuB,EACL;QAClB,MAAM,WAA8B,EAAE;QAEtC,iCAAiC;QACjC,SAAS,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,UAAU;QAErD,mCAAmC;QACnC,SAAS,IAAI,IAAI,IAAI,CAAC,6BAA6B,CAAC,SAAS,UAAU;QAEvE,2BAA2B;QAC3B,SAAS,IAAI,CAAC,IAAI,CAAC,8BAA8B,CAAC,SAAS;QAE3D,mCAAmC;QACnC,IAAI,SAAS,UAAU,KAAK,SAAS;YACnC,SAAS,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,SAAS;QACtD;QAEA,uCAAuC;QACvC,SAAS,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,SAAS;QAEpD,sBAAsB;QACtB,SAAS,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,SAAS;QAErD,0BAA0B;QAC1B,SAAS,IAAI,CAAC,IAAI,CAAC,4BAA4B,CAAC,SAAS;QAEzD,4BAA4B;QAC5B,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ;QAE/C,mBAAmB;QACnB,MAAM,UAAU,IAAI,CAAC,eAAe,CAAC,UAAU;QAE/C,+BAA+B;QAC/B,MAAM,oBAAoB,IAAI,CAAC,yBAAyB,CAAC,SAAS;QAElE,0BAA0B;QAC1B,MAAM,gBAAgB,IAAI,CAAC,qBAAqB,CAAC,SAAS;QAE1D,wBAAwB;QACxB,MAAM,cAAc,IAAI,CAAC,kBAAkB,CAAC;QAE5C,oBAAoB;QACpB,MAAM,aAAa,IAAI,CAAC,mBAAmB,CAAC,UAAU;QACtD,MAAM,eAAe,IAAI,CAAC,6BAA6B,CAAC,UAAU;QAClE,MAAM,oBAAoB,IAAI,CAAC,iBAAiB,CAAC;QAEjD,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACF;IACF;IAEQ,gBAAgB,KAAa,EAAY;QAC/C,MAAM,cAAc,IAAI,IAAI;YAAC;YAAK;YAAK;YAAM;YAAQ;YAAO;YAAM;YAAM;YAAO;YAAM;YAAM;YAAM;YAAM;YAAO;YAAM;YAAO;YAAQ;YAAQ;YAAO;YAAM;YAAK;YAAK;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAQ;SAAO;QAE3N,MAAM,QAAQ,MAAM,WAAW,GAC5B,OAAO,CAAC,YAAY,KACpB,KAAK,CAAC,OACN,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG,KAAK,CAAC,YAAY,GAAG,CAAC;QAEtD,yCAAyC;QACzC,MAAM,YAAoC,CAAC;QAC3C,MAAM,OAAO,CAAC,CAAA;YACZ,SAAS,CAAC,KAAK,GAAG,CAAC,SAAS,CAAC,KAAK,IAAI,CAAC,IAAI;QAC7C;QAEA,OAAO,OAAO,OAAO,CAAC,WACnB,IAAI,CAAC;gBAAC,GAAG,EAAE,UAAE,GAAG,EAAE;mBAAK,IAAI;WAC3B,KAAK,CAAC,GAAG,IACT,GAAG,CAAC;gBAAC,CAAC,KAAK;mBAAK;;IACrB;IAEQ,gBAAgB,KAAa,EAAY;QAC/C,MAAM,WAAqB,EAAE;QAE7B,0BAA0B;QAC1B,MAAM,aAAa;QACnB,MAAM,eAAe,MAAM,KAAK,CAAC;QACjC,IAAI,cAAc,SAAS,IAAI,IAAI;QAEnC,gBAAgB;QAChB,MAAM,YAAY;QAClB,MAAM,cAAc,MAAM,KAAK,CAAC;QAChC,IAAI,aAAa,SAAS,IAAI,IAAI;QAElC,sBAAsB;QACtB,MAAM,eAAe;QACrB,MAAM,iBAAiB,MAAM,KAAK,CAAC;QACnC,IAAI,gBAAgB,SAAS,IAAI,IAAI;QAErC,kDAAkD;QAClD,MAAM,aAAa;QACnB,MAAM,eAAe,MAAM,KAAK,CAAC;QACjC,IAAI,cAAc,SAAS,IAAI,IAAI,aAAa,KAAK,CAAC,GAAG,KAAI,uBAAuB;QAEpF,OAAO;eAAI,IAAI,IAAI;SAAU,CAAC,oBAAoB;;IACpD;IAEQ,iBAAiB,KAAa,EAAuC;QAC3E,MAAM,gBAAgB;YAAC;YAAO;YAAS;YAAa;YAAY;YAAW;YAAY;YAAc;YAAS;SAAW;QACzH,MAAM,gBAAgB;YAAC;YAAQ;YAAW;YAAY;YAAQ;YAAS;YAAa;YAAgB;YAAc;YAAW;SAAU;QAEvI,MAAM,QAAQ,MAAM,WAAW,GAAG,KAAK,CAAC;QACxC,IAAI,gBAAgB;QACpB,IAAI,gBAAgB;QAEpB,MAAM,OAAO,CAAC,CAAA;YACZ,IAAI,cAAc,IAAI,CAAC,CAAA,KAAM,KAAK,QAAQ,CAAC,MAAM;YACjD,IAAI,cAAc,IAAI,CAAC,CAAA,KAAM,KAAK,QAAQ,CAAC,MAAM;QACnD;QAEA,IAAI,gBAAgB,eAAe,OAAO;QAC1C,IAAI,gBAAgB,eAAe,OAAO;QAC1C,OAAO;IACT;IAEQ,iBAAiB,KAAa,EAAE,QAAkB,EAAyC;QACjG,MAAM,iBAAiB;YAAC;YAAO;YAAO;YAAO;YAAO;YAAO;YAAe;YAAa;YAAe;YAAa;YAAS;SAAY;QACxI,MAAM,uBAAuB,SAAS,MAAM,CAAC,CAAA,IAAK,eAAe,QAAQ,CAAC,EAAE,WAAW,KAAK,MAAM;QAElG,IAAI,MAAM,MAAM,GAAG,OAAO,yBAAyB,GAAG,OAAO;QAC7D,IAAI,MAAM,MAAM,GAAG,OAAO,uBAAuB,GAAG,OAAO;QAC3D,OAAO;IACT;IAEQ,gBAAgB,KAAa,EAAE,QAAkB,EAAU;QACjE,MAAM,iBAAiB;YACrB,mBAAmB;gBAAC;gBAAY;gBAAS;gBAAQ;aAAW;YAC5D,uBAAuB;gBAAC;gBAAW;gBAAe;gBAAa;aAAW;YAC1E,iBAAiB;gBAAC;gBAAY;gBAAc;gBAAc;aAAe;YACzE,YAAY;gBAAC;gBAAY;gBAAY;gBAAW;aAAa;YAC7D,UAAU;gBAAC;gBAAa;gBAAU;gBAAU;aAAS;YACrD,gBAAgB;gBAAC;gBAAY;gBAAQ;gBAAgB;aAAQ;QAC/D;QAEA,MAAM,aAAa,MAAM,WAAW;QACpC,KAAK,MAAM,CAAC,QAAQ,SAAS,IAAI,OAAO,OAAO,CAAC,gBAAiB;YAC/D,IAAI,SAAS,IAAI,CAAC,CAAA,UAAW,WAAW,QAAQ,CAAC,WAAW;gBAC1D,OAAO;YACT;QACF;QAEA,OAAO;IACT;IAEQ,eAAe,KAAa,EAAE,QAAkB,EAAuB;QAC7E,MAAM,UAA+B,CAAC;QAEtC,6BAA6B;QAC7B,MAAM,cAAc,MAAM,KAAK,CAAC;QAChC,IAAI,aAAa,QAAQ,MAAM,GAAG,WAAW,CAAC,EAAE;QAEhD,+BAA+B;QAC/B,MAAM,gBAAgB,MAAM,KAAK,CAAC;QAClC,IAAI,eAAe,QAAQ,QAAQ,GAAG,aAAa,CAAC,EAAE;QAEtD,0BAA0B;QAC1B,MAAM,gBAAgB,MAAM,KAAK,CAAC;QAClC,IAAI,eAAe,QAAQ,SAAS,GAAG,aAAa,CAAC,EAAE;QAEvD,mBAAmB;QACnB,MAAM,WAAW;YAAC;YAAa;YAAY;YAAU;YAAW;YAAY;SAAS;QACrF,MAAM,oBAAoB,SAAS,MAAM,CAAC,CAAA,UACxC,MAAM,WAAW,GAAG,QAAQ,CAAC;QAE/B,IAAI,kBAAkB,MAAM,GAAG,GAAG,QAAQ,QAAQ,GAAG;QAErD,OAAO;IACT;IAEQ,cAAc,KAAa,EAA6B;QAC9D,MAAM,cAAc;YAAC;YAAW;YAAY;YAAQ;YAAS;YAAW;SAAa;QACrF,MAAM,cAAc;YAAC;YAAS;YAAQ;YAAW;SAAS;QAE1D,MAAM,aAAa,MAAM,WAAW;QAEpC,IAAI,YAAY,IAAI,CAAC,CAAA,OAAQ,WAAW,QAAQ,CAAC,QAAQ,OAAO;QAChE,IAAI,YAAY,IAAI,CAAC,CAAA,OAAQ,WAAW,QAAQ,CAAC,QAAQ,OAAO;QAChE,OAAO;IACT;IAEQ,sBAAsB,KAAa,EAAE,QAAkB,EAAU;QACvE,IAAI,QAAQ;QACZ,MAAM,WAAW;QAEjB,gBAAgB;QAChB,IAAI,MAAM,MAAM,GAAG,IAAI,SAAS;QAChC,IAAI,MAAM,MAAM,GAAG,KAAK,SAAS;QAEjC,gBAAgB;QAChB,SAAS,KAAK,GAAG,CAAC,SAAS,MAAM,EAAE;QAEnC,mBAAmB;QACnB,IAAI,MAAM,QAAQ,CAAC,SAAS,MAAM,QAAQ,CAAC,QAAQ,MAAM,QAAQ,CAAC,MAAM,SAAS;QAEjF,kBAAkB;QAClB,IAAI,MAAM,QAAQ,CAAC,MAAM,SAAS;QAElC,iBAAiB;QACjB,IAAI,MAAM,WAAW,GAAG,QAAQ,CAAC,cAAc,MAAM,WAAW,GAAG,QAAQ,CAAC,aAAa,SAAS;QAElG,OAAO,KAAK,GAAG,CAAC,QAAQ,UAAU;IACpC;IAEA,oEAAoE;IAC5D,wBAAwB,QAAuB,EAAE,KAAa,EAAmB;QACvF,OAAO;YACL,OAAO;YACP,SAAS,AAAC,qBAA0D,OAAtC,SAAS,UAAU,EAAC,oBAAoD,OAAlC,SAAS,MAAM,EAAC,oBAAuD,OAArC,SAAS,OAAO,EAAC,sBAA4D,OAAxC,KAAK,KAAK,CAAC,SAAS,YAAY,GAAG,MAAK;YACnL,MAAM;YACN,UAAU;QACZ;IACF;IAEQ,8BAA8B,OAAe,EAAE,QAAuB,EAAE,KAAa,EAAqB;QAChH,kDAAkD;QAClD,OAAO,EAAE;IACX;IAEQ,+BAA+B,OAAe,EAAE,QAAuB,EAAmB;QAChG,OAAO;YACL,OAAO;YACP,SAAS;YACT,MAAM;YACN,UAAU;QACZ;IACF;IAEQ,wBAAwB,OAAe,EAAE,QAAuB,EAAmB;QACzF,OAAO;YACL,OAAO;YACP,SAAS;YACT,MAAM;YACN,UAAU;QACZ;IACF;IAEQ,wBAAwB,OAAe,EAAE,QAAuB,EAAmB;QACzF,OAAO;YACL,OAAO;YACP,SAAS;YACT,MAAM;YACN,UAAU;QACZ;IACF;IAEQ,yBAAyB,OAAe,EAAE,QAAuB,EAAmB;QAC1F,OAAO;YACL,OAAO;YACP,SAAS;YACT,MAAM;YACN,UAAU;QACZ;IACF;IAEQ,6BAA6B,OAAe,EAAE,QAAuB,EAAmB;QAC9F,OAAO;YACL,OAAO;YACP,SAAS;YACT,MAAM;YACN,UAAU;QACZ;IACF;IAEQ,gBAAgB,QAA2B,EAAE,QAAuB,EAAU;QACpF,MAAM,eAAyB,EAAE;QAEjC,qCAAqC;QACrC,IAAI,SAAS,UAAU,KAAK,YAAY;YACtC,aAAa,IAAI,CAAC;QACpB;QAEA,IAAI,SAAS,OAAO,KAAK,QAAQ;YAC/B,aAAa,IAAI,CAAC;QACpB;QAEA,IAAI,SAAS,YAAY,GAAG,KAAK;YAC/B,aAAa,IAAI,CAAC;QACpB;QAEA,IAAI,SAAS,OAAO,CAAC,MAAM,EAAE;YAC3B,MAAM,cAAc,WAAW,SAAS,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU;YACzE,IAAI,cAAc,QAAQ;gBACxB,aAAa,IAAI,CAAC;YACpB,OAAO,IAAI,cAAc,OAAO;gBAC9B,aAAa,IAAI,CAAC;YACpB;QACF;QAEA,IAAI,SAAS,WAAW,IAAI,SAAS,WAAW,CAAC,MAAM,GAAG,GAAG;YAC3D,aAAa,IAAI,CAAC,AAAC,GAA8B,OAA5B,SAAS,WAAW,CAAC,MAAM,EAAC;QACnD;QAEA,IAAI,SAAS,qBAAqB,IAAI,SAAS,qBAAqB,CAAC,MAAM,GAAG,GAAG;YAC/E,aAAa,IAAI,CAAC,AAAC,GAAwC,OAAtC,SAAS,qBAAqB,CAAC,MAAM,EAAC;QAC7D;QAEA,qCAAqC;QACrC,IAAI,UAAU;QAEd,IAAI,aAAa,MAAM,GAAG,GAAG;YAC3B,WAAW,OAAO,aAAa,IAAI,CAAC,MAAM,WAAW;QACvD;QAEA,WAAW,AAAC,KAAoB,OAAhB,SAAS,MAAM,EAAC;QAEhC,OAAO;IACT;IAEQ,0BAA0B,OAAe,EAAE,QAAuB,EAAY;QACpF,MAAM,YAAsB,EAAE;QAE9B,yCAAyC;QACzC,IAAI,CAAC,SAAS,OAAO,CAAC,MAAM,EAAE;YAC5B,UAAU,IAAI,CAAC;QACjB;QAEA,IAAI,CAAC,SAAS,OAAO,CAAC,QAAQ,EAAE;YAC9B,UAAU,IAAI,CAAC;QACjB;QAEA,IAAI,CAAC,SAAS,OAAO,CAAC,SAAS,IAAI,CAAC,SAAS,QAAQ,CAAC,QAAQ,CAAC,YAAY;YACzE,UAAU,IAAI,CAAC;QACjB;QAEA,IAAI,CAAC,SAAS,OAAO,CAAC,QAAQ,EAAE;YAC9B,UAAU,IAAI,CAAC;QACjB;QAEA,2BAA2B;QAC3B,IAAI,YAAY,eAAe;YAC7B,IAAI,SAAS,YAAY,GAAG,KAAK;gBAC/B,UAAU,IAAI,CAAC;YACjB;YACA,UAAU,IAAI,CAAC;QACjB,OAAO,IAAI,YAAY,gBAAgB;YACrC,UAAU,IAAI,CAAC;YACf,UAAU,IAAI,CAAC;QACjB,OAAO,IAAI,YAAY,SAAS;YAC9B,UAAU,IAAI,CAAC;YACf,UAAU,IAAI,CAAC;QACjB;QAEA,OAAO,UAAU,KAAK,CAAC,GAAG,GAAG,uBAAuB;;IACtD;IAEQ,sBAAsB,OAAe,EAAE,QAAuB,EAAY;QAChF,MAAM,SAAmB,EAAE;QAE3B,uCAAuC;QACvC,IAAI,SAAS,QAAQ,CAAC,QAAQ,CAAC,aAAa;YAC1C,OAAO,IAAI,CAAC,2BAA2B,oBAAoB;QAC7D;QAEA,IAAI,SAAS,QAAQ,CAAC,QAAQ,CAAC,cAAc,SAAS,OAAO,CAAC,QAAQ,EAAE;YACtE,OAAO,IAAI,CAAC,qBAAqB,wBAAwB;QAC3D;QAEA,IAAI,SAAS,OAAO,CAAC,MAAM,EAAE;YAC3B,OAAO,IAAI,CAAC,qBAAqB;QACnC;QAEA,wBAAwB;QACxB,IAAI,YAAY,eAAe;YAC7B,OAAO,IAAI,CAAC,qBAAqB,0BAA0B;QAC7D,OAAO,IAAI,YAAY,gBAAgB;YACrC,OAAO,IAAI,CAAC,4BAA4B,sBAAsB;QAChE,OAAO,IAAI,YAAY,SAAS;YAC9B,OAAO,IAAI,CAAC,uBAAuB,wBAAwB;QAC7D;QAEA,OAAO;eAAI,IAAI,IAAI;SAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,mCAAmC;;IAC7E;IAEQ,mBAAmB,QAA2B,EAAY;QAChE,MAAM,cAAwB,EAAE;QAEhC,6CAA6C;QAC7C,SAAS,OAAO,CAAC,CAAA;YACf,IAAI,QAAQ,IAAI,KAAK,gBAAgB,QAAQ,IAAI,KAAK,kBAAkB;gBACtE,MAAM,QAAQ,QAAQ,OAAO,CAAC,KAAK,CAAC;gBACpC,MAAM,OAAO,CAAC,CAAA;oBACZ,mEAAmE;oBACnE,IAAI,KAAK,KAAK,CAAC,oBACb,CAAC,KAAK,QAAQ,CAAC,cAAc,KAAK,QAAQ,CAAC,YAAY,KAAK,QAAQ,CAAC,kBACnE,KAAK,QAAQ,CAAC,cAAc,KAAK,QAAQ,CAAC,kBAAkB,KAAK,QAAQ,CAAC,aAAa,GAAG;wBAC5F,MAAM,YAAY,KAAK,OAAO,CAAC,iBAAiB,IAAI,IAAI;wBACxD,IAAI,UAAU,MAAM,GAAG,IAAI;4BACzB,YAAY,IAAI,CAAC;wBACnB;oBACF;gBACF;YACF;QACF;QAEA,yCAAyC;QACzC,IAAI,YAAY,MAAM,KAAK,GAAG;YAC5B,YAAY,IAAI,CACd,gCACA,gCACA,mCACA;QAEJ;QAEA,OAAO,YAAY,KAAK,CAAC,GAAG,GAAG,0BAA0B;;IAC3D;IAEQ,oBAAoB,QAAuB,EAAE,QAA2B,EAAU;QACxF,OAAO,KAAK,cAAc;;IAC5B;IAEQ,8BAA8B,QAA2B,EAAE,QAAuB,EAAU;QAClG,OAAO,KAAK,cAAc;;IAC5B;IAEQ,kBAAkB,QAA2B,EAAU;QAC7D,MAAM,aAAa,SAAS,MAAM,CAAC,CAAC,KAAK,UAAY,MAAM,QAAQ,OAAO,CAAC,KAAK,CAAC,KAAK,MAAM,EAAE;QAC9F,OAAO,KAAK,IAAI,CAAC,aAAa,KAAK,gCAAgC;;IACrE;IApeA,aAAsB,CAAE;AAqe1B;AAveE,yKADW,gBACI,YAAf,KAAA", "debugId": null}}, {"offset": {"line": 1185, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent3/agentepub/src/lib/agents/intelligenceLayer.ts"], "sourcesContent": ["/**\n * Input Analysis and Intelligence Layer\n * \n * This module provides advanced input analysis capabilities including\n * sentiment analysis, keyword extraction, intent recognition, and context understanding.\n */\n\nimport { InputAnalysis } from './responseEngine'\n\nexport interface AdvancedAnalysis extends InputAnalysis {\n  namedEntities: NamedEntity[]\n  topicClusters: TopicCluster[]\n  linguisticFeatures: LinguisticFeatures\n  businessContext: BusinessContext\n  riskFactors: RiskFactor[]\n  opportunityIndicators: OpportunityIndicator[]\n}\n\nexport interface NamedEntity {\n  text: string\n  type: 'PERSON' | 'ORGANIZATION' | 'LOCATION' | 'DATE' | 'MONEY' | 'PERCENTAGE' | 'PRODUCT' | 'SERVICE'\n  confidence: number\n  startIndex: number\n  endIndex: number\n}\n\nexport interface TopicCluster {\n  name: string\n  keywords: string[]\n  relevance: number\n  category: 'marketing' | 'business' | 'technical' | 'creative' | 'financial'\n}\n\nexport interface LinguisticFeatures {\n  readabilityScore: number\n  formalityLevel: 'informal' | 'neutral' | 'formal'\n  technicalComplexity: number\n  emotionalIntensity: number\n  questionCount: number\n  imperativeCount: number\n  averageSentenceLength: number\n}\n\nexport interface BusinessContext {\n  industry: string[]\n  businessStage: 'startup' | 'growth' | 'mature' | 'enterprise'\n  marketingMaturity: 'beginner' | 'intermediate' | 'advanced'\n  budgetRange: 'micro' | 'small' | 'medium' | 'large' | 'enterprise'\n  timeframe: 'immediate' | 'short-term' | 'medium-term' | 'long-term'\n}\n\nexport interface RiskFactor {\n  type: 'timeline' | 'budget' | 'scope' | 'technical' | 'market' | 'communication'\n  description: string\n  severity: 'low' | 'medium' | 'high' | 'critical'\n  probability: number\n  impact: number\n  mitigation: string[]\n}\n\nexport interface OpportunityIndicator {\n  type: 'growth' | 'efficiency' | 'innovation' | 'market' | 'competitive'\n  description: string\n  potential: 'low' | 'medium' | 'high'\n  effort: 'low' | 'medium' | 'high'\n  timeframe: 'immediate' | 'short' | 'medium' | 'long'\n}\n\nexport class IntelligenceLayer {\n  private static instance: IntelligenceLayer\n  \n  // Portuguese marketing/business vocabulary\n  private readonly marketingTerms = new Set([\n    'campanha', 'marketing', 'publicidade', 'propaganda', 'branding', 'brand',\n    'roi', 'ctr', 'cpm', 'cpc', 'impressões', 'cliques', 'conversões',\n    'segmentação', 'targeting', 'remarketing', 'lookalike', 'funil',\n    'awareness', 'consideração', 'conversão', 'retenção', 'advocacy'\n  ])\n\n  private readonly businessTerms = new Set([\n    'cliente', 'negócio', 'empresa', 'startup', 'corporação', 'b2b', 'b2c',\n    'receita', 'faturamento', 'lucro', 'margem', 'investimento', 'capital',\n    'mercado', 'concorrência', 'competidor', 'nicho', 'segmento'\n  ])\n\n  private readonly technicalTerms = new Set([\n    'api', 'crm', 'erp', 'analytics', 'dashboard', 'kpi', 'métrica',\n    'automação', 'integração', 'plataforma', 'sistema', 'ferramenta',\n    'pixel', 'tag', 'tracking', 'attribution', 'attribution modeling'\n  ])\n\n  private readonly urgencyIndicators = new Set([\n    'urgente', 'imediato', 'hoje', 'agora', 'já', 'rápido', 'asap',\n    'emergência', 'crítico', 'prioritário', 'ontem', 'deadline'\n  ])\n\n  private readonly positiveIndicators = new Set([\n    'ótimo', 'excelente', 'perfeito', 'maravilhoso', 'fantástico',\n    'sucesso', 'aprovado', 'satisfeito', 'feliz', 'positivo', 'bom'\n  ])\n\n  private readonly negativeIndicators = new Set([\n    'problema', 'erro', 'falha', 'ruim', 'péssimo', 'insatisfeito',\n    'preocupado', 'crítico', 'negativo', 'rejeitado', 'cancelado'\n  ])\n\n  private constructor() {}\n\n  public static getInstance(): IntelligenceLayer {\n    if (!IntelligenceLayer.instance) {\n      IntelligenceLayer.instance = new IntelligenceLayer()\n    }\n    return IntelligenceLayer.instance\n  }\n\n  public performAdvancedAnalysis(input: string): AdvancedAnalysis {\n    // Basic analysis first\n    const basicAnalysis = this.performBasicAnalysis(input)\n    \n    // Advanced analysis\n    const namedEntities = this.extractNamedEntities(input)\n    const topicClusters = this.identifyTopicClusters(input)\n    const linguisticFeatures = this.analyzeLinguisticFeatures(input)\n    const businessContext = this.inferBusinessContext(input, namedEntities, topicClusters)\n    const riskFactors = this.identifyRiskFactors(input, basicAnalysis, businessContext)\n    const opportunityIndicators = this.identifyOpportunities(input, basicAnalysis, businessContext)\n\n    return {\n      ...basicAnalysis,\n      namedEntities,\n      topicClusters,\n      linguisticFeatures,\n      businessContext,\n      riskFactors,\n      opportunityIndicators\n    }\n  }\n\n  private performBasicAnalysis(input: string): InputAnalysis {\n    const words = input.toLowerCase().split(/\\s+/)\n    const sentences = input.split(/[.!?]+/).filter(s => s.trim().length > 0)\n    \n    return {\n      keywords: this.extractKeywords(input),\n      entities: this.extractBasicEntities(input),\n      sentiment: this.analyzeSentiment(input),\n      complexity: this.assessComplexity(input),\n      intent: this.determineIntent(input),\n      context: this.extractContext(input),\n      urgency: this.assessUrgency(input),\n      completeness: this.calculateCompleteness(input)\n    }\n  }\n\n  private extractNamedEntities(input: string): NamedEntity[] {\n    const entities: NamedEntity[] = []\n    \n    // Money entities\n    const moneyRegex = /R\\$\\s*([\\d.,]+)|(\\d+(?:\\.\\d{3})*(?:,\\d{2})?)\\s*reais?/gi\n    let match\n    while ((match = moneyRegex.exec(input)) !== null) {\n      entities.push({\n        text: match[0],\n        type: 'MONEY',\n        confidence: 0.9,\n        startIndex: match.index,\n        endIndex: match.index + match[0].length\n      })\n    }\n\n    // Percentage entities\n    const percentRegex = /(\\d+(?:,\\d+)?)\\s*%/g\n    while ((match = percentRegex.exec(input)) !== null) {\n      entities.push({\n        text: match[0],\n        type: 'PERCENTAGE',\n        confidence: 0.95,\n        startIndex: match.index,\n        endIndex: match.index + match[0].length\n      })\n    }\n\n    // Date entities\n    const dateRegex = /(\\d{1,2}\\/\\d{1,2}\\/\\d{4}|\\d{1,2}\\s+de\\s+\\w+|\\w+\\s+\\d{1,2})/gi\n    while ((match = dateRegex.exec(input)) !== null) {\n      entities.push({\n        text: match[0],\n        type: 'DATE',\n        confidence: 0.8,\n        startIndex: match.index,\n        endIndex: match.index + match[0].length\n      })\n    }\n\n    // Organization entities (capitalized sequences)\n    const orgRegex = /\\b[A-Z][a-z]+(?:\\s+[A-Z][a-z]+)*\\b/g\n    while ((match = orgRegex.exec(input)) !== null) {\n      // Filter out common words that might be capitalized\n      const commonWords = ['Instagram', 'Facebook', 'Google', 'YouTube', 'LinkedIn', 'TikTok']\n      if (match[0].length > 3 && !commonWords.includes(match[0])) {\n        entities.push({\n          text: match[0],\n          type: 'ORGANIZATION',\n          confidence: 0.6,\n          startIndex: match.index,\n          endIndex: match.index + match[0].length\n        })\n      }\n    }\n\n    return entities\n  }\n\n  private identifyTopicClusters(input: string): TopicCluster[] {\n    const words = input.toLowerCase().split(/\\s+/)\n    const clusters: TopicCluster[] = []\n\n    // Marketing cluster\n    const marketingWords = words.filter(word => this.marketingTerms.has(word))\n    if (marketingWords.length > 0) {\n      clusters.push({\n        name: 'Marketing Digital',\n        keywords: marketingWords,\n        relevance: marketingWords.length / words.length,\n        category: 'marketing'\n      })\n    }\n\n    // Business cluster\n    const businessWords = words.filter(word => this.businessTerms.has(word))\n    if (businessWords.length > 0) {\n      clusters.push({\n        name: 'Negócios',\n        keywords: businessWords,\n        relevance: businessWords.length / words.length,\n        category: 'business'\n      })\n    }\n\n    // Technical cluster\n    const technicalWords = words.filter(word => this.technicalTerms.has(word))\n    if (technicalWords.length > 0) {\n      clusters.push({\n        name: 'Técnico',\n        keywords: technicalWords,\n        relevance: technicalWords.length / words.length,\n        category: 'technical'\n      })\n    }\n\n    return clusters.sort((a, b) => b.relevance - a.relevance)\n  }\n\n  private analyzeLinguisticFeatures(input: string): LinguisticFeatures {\n    const sentences = input.split(/[.!?]+/).filter(s => s.trim().length > 0)\n    const words = input.split(/\\s+/)\n    \n    // Count questions and imperatives\n    const questionCount = (input.match(/\\?/g) || []).length\n    const imperativeCount = this.countImperatives(input)\n    \n    // Calculate average sentence length\n    const averageSentenceLength = sentences.length > 0 ? \n      sentences.reduce((sum, s) => sum + s.split(/\\s+/).length, 0) / sentences.length : 0\n\n    // Assess formality\n    const formalityLevel = this.assessFormality(input)\n    \n    // Technical complexity based on technical terms\n    const technicalWords = words.filter(word => this.technicalTerms.has(word.toLowerCase()))\n    const technicalComplexity = technicalWords.length / words.length\n\n    // Emotional intensity based on emotional words\n    const emotionalWords = words.filter(word => \n      this.positiveIndicators.has(word.toLowerCase()) || \n      this.negativeIndicators.has(word.toLowerCase())\n    )\n    const emotionalIntensity = emotionalWords.length / words.length\n\n    // Simple readability score (based on sentence length and word complexity)\n    const readabilityScore = this.calculateReadabilityScore(sentences, words)\n\n    return {\n      readabilityScore,\n      formalityLevel,\n      technicalComplexity,\n      emotionalIntensity,\n      questionCount,\n      imperativeCount,\n      averageSentenceLength\n    }\n  }\n\n  private inferBusinessContext(\n    input: string, \n    entities: NamedEntity[], \n    clusters: TopicCluster[]\n  ): BusinessContext {\n    // Infer industry from topic clusters and keywords\n    const industry: string[] = []\n    clusters.forEach(cluster => {\n      if (cluster.category === 'marketing') industry.push('Marketing Digital')\n      if (cluster.category === 'business') industry.push('Consultoria')\n      if (cluster.category === 'technical') industry.push('Tecnologia')\n    })\n\n    // Infer budget range from money entities\n    let budgetRange: BusinessContext['budgetRange'] = 'small'\n    const moneyEntities = entities.filter(e => e.type === 'MONEY')\n    if (moneyEntities.length > 0) {\n      const amounts = moneyEntities.map(e => {\n        const numStr = e.text.replace(/[^\\d,]/g, '').replace(',', '.')\n        return parseFloat(numStr) || 0\n      })\n      const maxAmount = Math.max(...amounts)\n      \n      if (maxAmount < 5000) budgetRange = 'micro'\n      else if (maxAmount < 25000) budgetRange = 'small'\n      else if (maxAmount < 100000) budgetRange = 'medium'\n      else if (maxAmount < 500000) budgetRange = 'large'\n      else budgetRange = 'enterprise'\n    }\n\n    // Infer business stage from language and complexity\n    let businessStage: BusinessContext['businessStage'] = 'growth'\n    const words = input.toLowerCase()\n    if (words.includes('startup') || words.includes('começando')) businessStage = 'startup'\n    else if (words.includes('empresa') || words.includes('corporação')) businessStage = 'mature'\n    else if (words.includes('multinacional') || words.includes('holding')) businessStage = 'enterprise'\n\n    // Infer marketing maturity from technical terms usage\n    const technicalCluster = clusters.find(c => c.category === 'technical')\n    let marketingMaturity: BusinessContext['marketingMaturity'] = 'intermediate'\n    if (!technicalCluster || technicalCluster.relevance < 0.1) marketingMaturity = 'beginner'\n    else if (technicalCluster.relevance > 0.3) marketingMaturity = 'advanced'\n\n    // Infer timeframe from urgency and date entities\n    let timeframe: BusinessContext['timeframe'] = 'medium-term'\n    const urgencyWords = input.toLowerCase().split(/\\s+/).filter(word => this.urgencyIndicators.has(word))\n    if (urgencyWords.length > 0) timeframe = 'immediate'\n    else if (entities.some(e => e.type === 'DATE')) timeframe = 'short-term'\n\n    return {\n      industry,\n      businessStage,\n      marketingMaturity,\n      budgetRange,\n      timeframe\n    }\n  }\n\n  private identifyRiskFactors(\n    input: string, \n    analysis: InputAnalysis, \n    context: BusinessContext\n  ): RiskFactor[] {\n    const risks: RiskFactor[] = []\n\n    // Timeline risks\n    if (analysis.urgency === 'high' && analysis.complexity === 'advanced') {\n      risks.push({\n        type: 'timeline',\n        description: 'Projeto complexo com prazo apertado pode comprometer qualidade',\n        severity: 'high',\n        probability: 0.8,\n        impact: 0.9,\n        mitigation: [\n          'Alocar recursos adicionais',\n          'Simplificar escopo inicial',\n          'Estabelecer marcos intermediários'\n        ]\n      })\n    }\n\n    // Budget risks\n    if (context.budgetRange === 'micro' && analysis.complexity === 'advanced') {\n      risks.push({\n        type: 'budget',\n        description: 'Orçamento limitado para projeto complexo',\n        severity: 'medium',\n        probability: 0.7,\n        impact: 0.8,\n        mitigation: [\n          'Redefinir escopo',\n          'Implementação em fases',\n          'Buscar orçamento adicional'\n        ]\n      })\n    }\n\n    // Communication risks\n    if (analysis.completeness < 0.5) {\n      risks.push({\n        type: 'communication',\n        description: 'Briefing incompleto pode gerar mal-entendidos',\n        severity: 'medium',\n        probability: 0.6,\n        impact: 0.7,\n        mitigation: [\n          'Reunião de alinhamento urgente',\n          'Checklist de informações',\n          'Validação constante com cliente'\n        ]\n      })\n    }\n\n    return risks\n  }\n\n  private identifyOpportunities(\n    input: string, \n    analysis: InputAnalysis, \n    context: BusinessContext\n  ): OpportunityIndicator[] {\n    const opportunities: OpportunityIndicator[] = []\n\n    // Growth opportunities\n    if (context.businessStage === 'startup' && context.budgetRange !== 'micro') {\n      opportunities.push({\n        type: 'growth',\n        description: 'Startup com orçamento adequado tem potencial de crescimento acelerado',\n        potential: 'high',\n        effort: 'medium',\n        timeframe: 'short'\n      })\n    }\n\n    // Efficiency opportunities\n    if (context.marketingMaturity === 'beginner' && analysis.complexity === 'basic') {\n      opportunities.push({\n        type: 'efficiency',\n        description: 'Implementação de processos básicos pode gerar ganhos rápidos',\n        potential: 'medium',\n        effort: 'low',\n        timeframe: 'immediate'\n      })\n    }\n\n    // Innovation opportunities\n    if (context.marketingMaturity === 'advanced' && analysis.keywords.includes('inovação')) {\n      opportunities.push({\n        type: 'innovation',\n        description: 'Cliente avançado aberto a soluções inovadoras',\n        potential: 'high',\n        effort: 'high',\n        timeframe: 'medium'\n      })\n    }\n\n    return opportunities\n  }\n\n  // Helper methods\n  private extractKeywords(input: string): string[] {\n    const commonWords = new Set(['o', 'a', 'de', 'para', 'com', 'em', 'um', 'uma', 'do', 'da', 'no', 'na', 'por', 'se', 'que', 'como', 'mais', 'mas', 'ou', 'e', 'é', 'são', 'foi', 'ser', 'ter', 'seu', 'sua', 'seus', 'suas'])\n    \n    const words = input.toLowerCase()\n      .replace(/[^\\w\\s]/g, ' ')\n      .split(/\\s+/)\n      .filter(word => word.length > 2 && !commonWords.has(word))\n    \n    const frequency: Record<string, number> = {}\n    words.forEach(word => {\n      frequency[word] = (frequency[word] || 0) + 1\n    })\n    \n    return Object.entries(frequency)\n      .sort(([,a], [,b]) => b - a)\n      .slice(0, 10)\n      .map(([word]) => word)\n  }\n\n  private extractBasicEntities(input: string): string[] {\n    const entities: string[] = []\n    \n    const moneyRegex = /R\\$\\s*[\\d.,]+|[\\d.,]+\\s*reais?/gi\n    const moneyMatches = input.match(moneyRegex)\n    if (moneyMatches) entities.push(...moneyMatches)\n    \n    const dateRegex = /\\d{1,2}\\/\\d{1,2}\\/\\d{4}|\\d{1,2}\\s+de\\s+\\w+|\\w+\\s+\\d{1,2}/gi\n    const dateMatches = input.match(dateRegex)\n    if (dateMatches) entities.push(...dateMatches)\n    \n    const percentRegex = /\\d+%/g\n    const percentMatches = input.match(percentRegex)\n    if (percentMatches) entities.push(...percentMatches)\n    \n    return [...new Set(entities)]\n  }\n\n  private analyzeSentiment(input: string): 'positive' | 'neutral' | 'negative' {\n    const words = input.toLowerCase().split(/\\s+/)\n    let positiveScore = 0\n    let negativeScore = 0\n    \n    words.forEach(word => {\n      if (this.positiveIndicators.has(word)) positiveScore++\n      if (this.negativeIndicators.has(word)) negativeScore++\n    })\n    \n    if (positiveScore > negativeScore) return 'positive'\n    if (negativeScore > positiveScore) return 'negative'\n    return 'neutral'\n  }\n\n  private assessComplexity(input: string): 'basic' | 'intermediate' | 'advanced' {\n    const words = input.toLowerCase().split(/\\s+/)\n    const technicalTermCount = words.filter(word => this.technicalTerms.has(word)).length\n    \n    if (input.length < 100 && technicalTermCount === 0) return 'basic'\n    if (input.length > 300 || technicalTermCount > 2) return 'advanced'\n    return 'intermediate'\n  }\n\n  private determineIntent(input: string): string {\n    const inputLower = input.toLowerCase()\n    \n    if (inputLower.includes('briefing') || inputLower.includes('criar')) return 'create_briefing'\n    if (inputLower.includes('análise') || inputLower.includes('performance')) return 'analyze_performance'\n    if (inputLower.includes('planejar') || inputLower.includes('estratégia')) return 'plan_campaign'\n    if (inputLower.includes('otimizar') || inputLower.includes('melhorar')) return 'optimize'\n    if (inputLower.includes('relatório') || inputLower.includes('report')) return 'report'\n    if (inputLower.includes('problema') || inputLower.includes('erro')) return 'troubleshoot'\n    \n    return 'general_inquiry'\n  }\n\n  private extractContext(input: string): Record<string, any> {\n    const context: Record<string, any> = {}\n    \n    const budgetMatch = input.match(/(?:budget|orçamento).*?R\\$\\s*([\\d.,]+)/i)\n    if (budgetMatch) context.budget = budgetMatch[1]\n    \n    const timelineMatch = input.match(/(\\d+)\\s*(?:dias?|semanas?|meses?)/i)\n    if (timelineMatch) context.timeline = timelineMatch[0]\n    \n    const audienceMatch = input.match(/(?:público|target|audiência).*?(\\d+[-–]\\d+\\s*anos?)/i)\n    if (audienceMatch) context.targetAge = audienceMatch[1]\n    \n    const channels = ['instagram', 'facebook', 'google', 'youtube', 'linkedin', 'tiktok']\n    const mentionedChannels = channels.filter(channel => \n      input.toLowerCase().includes(channel)\n    )\n    if (mentionedChannels.length > 0) context.channels = mentionedChannels\n    \n    return context\n  }\n\n  private assessUrgency(input: string): 'low' | 'medium' | 'high' {\n    const words = input.toLowerCase().split(/\\s+/)\n    const urgentWords = words.filter(word => this.urgencyIndicators.has(word))\n    \n    if (urgentWords.length > 0) return 'high'\n    if (input.toLowerCase().includes('breve') || input.toLowerCase().includes('próximo')) return 'medium'\n    return 'low'\n  }\n\n  private calculateCompleteness(input: string): number {\n    let score = 0\n    const maxScore = 10\n    \n    if (input.length > 50) score += 2\n    if (input.length > 150) score += 2\n    if (input.includes('R$') || input.toLowerCase().includes('orçamento')) score += 2\n    if (input.match(/\\d+\\s*(?:dias?|semanas?|meses?)/)) score += 2\n    if (input.toLowerCase().includes('público') || input.toLowerCase().includes('target')) score += 1\n    if (input.toLowerCase().includes('cliente') || input.toLowerCase().includes('campanha')) score += 1\n    \n    return Math.min(score / maxScore, 1)\n  }\n\n  private countImperatives(input: string): number {\n    const imperativePatterns = [\n      /\\b(?:faça|crie|desenvolva|implemente|execute|realize)\\b/gi,\n      /\\b(?:preciso|quero|gostaria|solicito)\\b/gi\n    ]\n    \n    return imperativePatterns.reduce((count, pattern) => {\n      const matches = input.match(pattern)\n      return count + (matches ? matches.length : 0)\n    }, 0)\n  }\n\n  private assessFormality(input: string): 'informal' | 'neutral' | 'formal' {\n    const informalIndicators = ['oi', 'olá', 'beleza', 'valeu', 'obrigado', 'brigado']\n    const formalIndicators = ['prezado', 'cordialmente', 'atenciosamente', 'solicito', 'gostaria']\n    \n    const words = input.toLowerCase().split(/\\s+/)\n    const informalCount = words.filter(word => informalIndicators.includes(word)).length\n    const formalCount = words.filter(word => formalIndicators.includes(word)).length\n    \n    if (formalCount > informalCount) return 'formal'\n    if (informalCount > formalCount) return 'informal'\n    return 'neutral'\n  }\n\n  private calculateReadabilityScore(sentences: string[], words: string[]): number {\n    if (sentences.length === 0 || words.length === 0) return 0\n    \n    const avgSentenceLength = words.length / sentences.length\n    const complexWords = words.filter(word => word.length > 6).length\n    const complexWordRatio = complexWords / words.length\n    \n    // Simple readability formula (higher score = easier to read)\n    return Math.max(0, 100 - (avgSentenceLength * 1.5) - (complexWordRatio * 100))\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;;;AA+DM,MAAM;IAwCX,OAAc,cAAiC;QAC7C,IAAI,CAAC,kBAAkB,QAAQ,EAAE;YAC/B,kBAAkB,QAAQ,GAAG,IAAI;QACnC;QACA,OAAO,kBAAkB,QAAQ;IACnC;IAEO,wBAAwB,KAAa,EAAoB;QAC9D,uBAAuB;QACvB,MAAM,gBAAgB,IAAI,CAAC,oBAAoB,CAAC;QAEhD,oBAAoB;QACpB,MAAM,gBAAgB,IAAI,CAAC,oBAAoB,CAAC;QAChD,MAAM,gBAAgB,IAAI,CAAC,qBAAqB,CAAC;QACjD,MAAM,qBAAqB,IAAI,CAAC,yBAAyB,CAAC;QAC1D,MAAM,kBAAkB,IAAI,CAAC,oBAAoB,CAAC,OAAO,eAAe;QACxE,MAAM,cAAc,IAAI,CAAC,mBAAmB,CAAC,OAAO,eAAe;QACnE,MAAM,wBAAwB,IAAI,CAAC,qBAAqB,CAAC,OAAO,eAAe;QAE/E,OAAO;YACL,GAAG,aAAa;YAChB;YACA;YACA;YACA;YACA;YACA;QACF;IACF;IAEQ,qBAAqB,KAAa,EAAiB;QACzD,MAAM,QAAQ,MAAM,WAAW,GAAG,KAAK,CAAC;QACxC,MAAM,YAAY,MAAM,KAAK,CAAC,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,GAAG,MAAM,GAAG;QAEtE,OAAO;YACL,UAAU,IAAI,CAAC,eAAe,CAAC;YAC/B,UAAU,IAAI,CAAC,oBAAoB,CAAC;YACpC,WAAW,IAAI,CAAC,gBAAgB,CAAC;YACjC,YAAY,IAAI,CAAC,gBAAgB,CAAC;YAClC,QAAQ,IAAI,CAAC,eAAe,CAAC;YAC7B,SAAS,IAAI,CAAC,cAAc,CAAC;YAC7B,SAAS,IAAI,CAAC,aAAa,CAAC;YAC5B,cAAc,IAAI,CAAC,qBAAqB,CAAC;QAC3C;IACF;IAEQ,qBAAqB,KAAa,EAAiB;QACzD,MAAM,WAA0B,EAAE;QAElC,iBAAiB;QACjB,MAAM,aAAa;QACnB,IAAI;QACJ,MAAO,CAAC,QAAQ,WAAW,IAAI,CAAC,MAAM,MAAM,KAAM;YAChD,SAAS,IAAI,CAAC;gBACZ,MAAM,KAAK,CAAC,EAAE;gBACd,MAAM;gBACN,YAAY;gBACZ,YAAY,MAAM,KAAK;gBACvB,UAAU,MAAM,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,MAAM;YACzC;QACF;QAEA,sBAAsB;QACtB,MAAM,eAAe;QACrB,MAAO,CAAC,QAAQ,aAAa,IAAI,CAAC,MAAM,MAAM,KAAM;YAClD,SAAS,IAAI,CAAC;gBACZ,MAAM,KAAK,CAAC,EAAE;gBACd,MAAM;gBACN,YAAY;gBACZ,YAAY,MAAM,KAAK;gBACvB,UAAU,MAAM,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,MAAM;YACzC;QACF;QAEA,gBAAgB;QAChB,MAAM,YAAY;QAClB,MAAO,CAAC,QAAQ,UAAU,IAAI,CAAC,MAAM,MAAM,KAAM;YAC/C,SAAS,IAAI,CAAC;gBACZ,MAAM,KAAK,CAAC,EAAE;gBACd,MAAM;gBACN,YAAY;gBACZ,YAAY,MAAM,KAAK;gBACvB,UAAU,MAAM,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,MAAM;YACzC;QACF;QAEA,gDAAgD;QAChD,MAAM,WAAW;QACjB,MAAO,CAAC,QAAQ,SAAS,IAAI,CAAC,MAAM,MAAM,KAAM;YAC9C,oDAAoD;YACpD,MAAM,cAAc;gBAAC;gBAAa;gBAAY;gBAAU;gBAAW;gBAAY;aAAS;YACxF,IAAI,KAAK,CAAC,EAAE,CAAC,MAAM,GAAG,KAAK,CAAC,YAAY,QAAQ,CAAC,KAAK,CAAC,EAAE,GAAG;gBAC1D,SAAS,IAAI,CAAC;oBACZ,MAAM,KAAK,CAAC,EAAE;oBACd,MAAM;oBACN,YAAY;oBACZ,YAAY,MAAM,KAAK;oBACvB,UAAU,MAAM,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,MAAM;gBACzC;YACF;QACF;QAEA,OAAO;IACT;IAEQ,sBAAsB,KAAa,EAAkB;QAC3D,MAAM,QAAQ,MAAM,WAAW,GAAG,KAAK,CAAC;QACxC,MAAM,WAA2B,EAAE;QAEnC,oBAAoB;QACpB,MAAM,iBAAiB,MAAM,MAAM,CAAC,CAAA,OAAQ,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC;QACpE,IAAI,eAAe,MAAM,GAAG,GAAG;YAC7B,SAAS,IAAI,CAAC;gBACZ,MAAM;gBACN,UAAU;gBACV,WAAW,eAAe,MAAM,GAAG,MAAM,MAAM;gBAC/C,UAAU;YACZ;QACF;QAEA,mBAAmB;QACnB,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA,OAAQ,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;QAClE,IAAI,cAAc,MAAM,GAAG,GAAG;YAC5B,SAAS,IAAI,CAAC;gBACZ,MAAM;gBACN,UAAU;gBACV,WAAW,cAAc,MAAM,GAAG,MAAM,MAAM;gBAC9C,UAAU;YACZ;QACF;QAEA,oBAAoB;QACpB,MAAM,iBAAiB,MAAM,MAAM,CAAC,CAAA,OAAQ,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC;QACpE,IAAI,eAAe,MAAM,GAAG,GAAG;YAC7B,SAAS,IAAI,CAAC;gBACZ,MAAM;gBACN,UAAU;gBACV,WAAW,eAAe,MAAM,GAAG,MAAM,MAAM;gBAC/C,UAAU;YACZ;QACF;QAEA,OAAO,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,SAAS,GAAG,EAAE,SAAS;IAC1D;IAEQ,0BAA0B,KAAa,EAAsB;QACnE,MAAM,YAAY,MAAM,KAAK,CAAC,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,GAAG,MAAM,GAAG;QACtE,MAAM,QAAQ,MAAM,KAAK,CAAC;QAE1B,kCAAkC;QAClC,MAAM,gBAAgB,CAAC,MAAM,KAAK,CAAC,UAAU,EAAE,EAAE,MAAM;QACvD,MAAM,kBAAkB,IAAI,CAAC,gBAAgB,CAAC;QAE9C,oCAAoC;QACpC,MAAM,wBAAwB,UAAU,MAAM,GAAG,IAC/C,UAAU,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,KAAK,CAAC,OAAO,MAAM,EAAE,KAAK,UAAU,MAAM,GAAG;QAEpF,mBAAmB;QACnB,MAAM,iBAAiB,IAAI,CAAC,eAAe,CAAC;QAE5C,gDAAgD;QAChD,MAAM,iBAAiB,MAAM,MAAM,CAAC,CAAA,OAAQ,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,WAAW;QACpF,MAAM,sBAAsB,eAAe,MAAM,GAAG,MAAM,MAAM;QAEhE,+CAA+C;QAC/C,MAAM,iBAAiB,MAAM,MAAM,CAAC,CAAA,OAClC,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,KAAK,WAAW,OAC5C,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,KAAK,WAAW;QAE9C,MAAM,qBAAqB,eAAe,MAAM,GAAG,MAAM,MAAM;QAE/D,0EAA0E;QAC1E,MAAM,mBAAmB,IAAI,CAAC,yBAAyB,CAAC,WAAW;QAEnE,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;YACA;QACF;IACF;IAEQ,qBACN,KAAa,EACb,QAAuB,EACvB,QAAwB,EACP;QACjB,kDAAkD;QAClD,MAAM,WAAqB,EAAE;QAC7B,SAAS,OAAO,CAAC,CAAA;YACf,IAAI,QAAQ,QAAQ,KAAK,aAAa,SAAS,IAAI,CAAC;YACpD,IAAI,QAAQ,QAAQ,KAAK,YAAY,SAAS,IAAI,CAAC;YACnD,IAAI,QAAQ,QAAQ,KAAK,aAAa,SAAS,IAAI,CAAC;QACtD;QAEA,yCAAyC;QACzC,IAAI,cAA8C;QAClD,MAAM,gBAAgB,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK;QACtD,IAAI,cAAc,MAAM,GAAG,GAAG;YAC5B,MAAM,UAAU,cAAc,GAAG,CAAC,CAAA;gBAChC,MAAM,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,KAAK;gBAC1D,OAAO,WAAW,WAAW;YAC/B;YACA,MAAM,YAAY,KAAK,GAAG,IAAI;YAE9B,IAAI,YAAY,MAAM,cAAc;iBAC/B,IAAI,YAAY,OAAO,cAAc;iBACrC,IAAI,YAAY,QAAQ,cAAc;iBACtC,IAAI,YAAY,QAAQ,cAAc;iBACtC,cAAc;QACrB;QAEA,oDAAoD;QACpD,IAAI,gBAAkD;QACtD,MAAM,QAAQ,MAAM,WAAW;QAC/B,IAAI,MAAM,QAAQ,CAAC,cAAc,MAAM,QAAQ,CAAC,cAAc,gBAAgB;aACzE,IAAI,MAAM,QAAQ,CAAC,cAAc,MAAM,QAAQ,CAAC,eAAe,gBAAgB;aAC/E,IAAI,MAAM,QAAQ,CAAC,oBAAoB,MAAM,QAAQ,CAAC,YAAY,gBAAgB;QAEvF,sDAAsD;QACtD,MAAM,mBAAmB,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK;QAC3D,IAAI,oBAA0D;QAC9D,IAAI,CAAC,oBAAoB,iBAAiB,SAAS,GAAG,KAAK,oBAAoB;aAC1E,IAAI,iBAAiB,SAAS,GAAG,KAAK,oBAAoB;QAE/D,iDAAiD;QACjD,IAAI,YAA0C;QAC9C,MAAM,eAAe,MAAM,WAAW,GAAG,KAAK,CAAC,OAAO,MAAM,CAAC,CAAA,OAAQ,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC;QAChG,IAAI,aAAa,MAAM,GAAG,GAAG,YAAY;aACpC,IAAI,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,SAAS,YAAY;QAE5D,OAAO;YACL;YACA;YACA;YACA;YACA;QACF;IACF;IAEQ,oBACN,KAAa,EACb,QAAuB,EACvB,OAAwB,EACV;QACd,MAAM,QAAsB,EAAE;QAE9B,iBAAiB;QACjB,IAAI,SAAS,OAAO,KAAK,UAAU,SAAS,UAAU,KAAK,YAAY;YACrE,MAAM,IAAI,CAAC;gBACT,MAAM;gBACN,aAAa;gBACb,UAAU;gBACV,aAAa;gBACb,QAAQ;gBACR,YAAY;oBACV;oBACA;oBACA;iBACD;YACH;QACF;QAEA,eAAe;QACf,IAAI,QAAQ,WAAW,KAAK,WAAW,SAAS,UAAU,KAAK,YAAY;YACzE,MAAM,IAAI,CAAC;gBACT,MAAM;gBACN,aAAa;gBACb,UAAU;gBACV,aAAa;gBACb,QAAQ;gBACR,YAAY;oBACV;oBACA;oBACA;iBACD;YACH;QACF;QAEA,sBAAsB;QACtB,IAAI,SAAS,YAAY,GAAG,KAAK;YAC/B,MAAM,IAAI,CAAC;gBACT,MAAM;gBACN,aAAa;gBACb,UAAU;gBACV,aAAa;gBACb,QAAQ;gBACR,YAAY;oBACV;oBACA;oBACA;iBACD;YACH;QACF;QAEA,OAAO;IACT;IAEQ,sBACN,KAAa,EACb,QAAuB,EACvB,OAAwB,EACA;QACxB,MAAM,gBAAwC,EAAE;QAEhD,uBAAuB;QACvB,IAAI,QAAQ,aAAa,KAAK,aAAa,QAAQ,WAAW,KAAK,SAAS;YAC1E,cAAc,IAAI,CAAC;gBACjB,MAAM;gBACN,aAAa;gBACb,WAAW;gBACX,QAAQ;gBACR,WAAW;YACb;QACF;QAEA,2BAA2B;QAC3B,IAAI,QAAQ,iBAAiB,KAAK,cAAc,SAAS,UAAU,KAAK,SAAS;YAC/E,cAAc,IAAI,CAAC;gBACjB,MAAM;gBACN,aAAa;gBACb,WAAW;gBACX,QAAQ;gBACR,WAAW;YACb;QACF;QAEA,2BAA2B;QAC3B,IAAI,QAAQ,iBAAiB,KAAK,cAAc,SAAS,QAAQ,CAAC,QAAQ,CAAC,aAAa;YACtF,cAAc,IAAI,CAAC;gBACjB,MAAM;gBACN,aAAa;gBACb,WAAW;gBACX,QAAQ;gBACR,WAAW;YACb;QACF;QAEA,OAAO;IACT;IAEA,iBAAiB;IACT,gBAAgB,KAAa,EAAY;QAC/C,MAAM,cAAc,IAAI,IAAI;YAAC;YAAK;YAAK;YAAM;YAAQ;YAAO;YAAM;YAAM;YAAO;YAAM;YAAM;YAAM;YAAM;YAAO;YAAM;YAAO;YAAQ;YAAQ;YAAO;YAAM;YAAK;YAAK;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAQ;SAAO;QAE3N,MAAM,QAAQ,MAAM,WAAW,GAC5B,OAAO,CAAC,YAAY,KACpB,KAAK,CAAC,OACN,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG,KAAK,CAAC,YAAY,GAAG,CAAC;QAEtD,MAAM,YAAoC,CAAC;QAC3C,MAAM,OAAO,CAAC,CAAA;YACZ,SAAS,CAAC,KAAK,GAAG,CAAC,SAAS,CAAC,KAAK,IAAI,CAAC,IAAI;QAC7C;QAEA,OAAO,OAAO,OAAO,CAAC,WACnB,IAAI,CAAC;gBAAC,GAAE,EAAE,UAAE,GAAE,EAAE;mBAAK,IAAI;WACzB,KAAK,CAAC,GAAG,IACT,GAAG,CAAC;gBAAC,CAAC,KAAK;mBAAK;;IACrB;IAEQ,qBAAqB,KAAa,EAAY;QACpD,MAAM,WAAqB,EAAE;QAE7B,MAAM,aAAa;QACnB,MAAM,eAAe,MAAM,KAAK,CAAC;QACjC,IAAI,cAAc,SAAS,IAAI,IAAI;QAEnC,MAAM,YAAY;QAClB,MAAM,cAAc,MAAM,KAAK,CAAC;QAChC,IAAI,aAAa,SAAS,IAAI,IAAI;QAElC,MAAM,eAAe;QACrB,MAAM,iBAAiB,MAAM,KAAK,CAAC;QACnC,IAAI,gBAAgB,SAAS,IAAI,IAAI;QAErC,OAAO;eAAI,IAAI,IAAI;SAAU;IAC/B;IAEQ,iBAAiB,KAAa,EAAuC;QAC3E,MAAM,QAAQ,MAAM,WAAW,GAAG,KAAK,CAAC;QACxC,IAAI,gBAAgB;QACpB,IAAI,gBAAgB;QAEpB,MAAM,OAAO,CAAC,CAAA;YACZ,IAAI,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,OAAO;YACvC,IAAI,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,OAAO;QACzC;QAEA,IAAI,gBAAgB,eAAe,OAAO;QAC1C,IAAI,gBAAgB,eAAe,OAAO;QAC1C,OAAO;IACT;IAEQ,iBAAiB,KAAa,EAAyC;QAC7E,MAAM,QAAQ,MAAM,WAAW,GAAG,KAAK,CAAC;QACxC,MAAM,qBAAqB,MAAM,MAAM,CAAC,CAAA,OAAQ,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,MAAM;QAErF,IAAI,MAAM,MAAM,GAAG,OAAO,uBAAuB,GAAG,OAAO;QAC3D,IAAI,MAAM,MAAM,GAAG,OAAO,qBAAqB,GAAG,OAAO;QACzD,OAAO;IACT;IAEQ,gBAAgB,KAAa,EAAU;QAC7C,MAAM,aAAa,MAAM,WAAW;QAEpC,IAAI,WAAW,QAAQ,CAAC,eAAe,WAAW,QAAQ,CAAC,UAAU,OAAO;QAC5E,IAAI,WAAW,QAAQ,CAAC,cAAc,WAAW,QAAQ,CAAC,gBAAgB,OAAO;QACjF,IAAI,WAAW,QAAQ,CAAC,eAAe,WAAW,QAAQ,CAAC,eAAe,OAAO;QACjF,IAAI,WAAW,QAAQ,CAAC,eAAe,WAAW,QAAQ,CAAC,aAAa,OAAO;QAC/E,IAAI,WAAW,QAAQ,CAAC,gBAAgB,WAAW,QAAQ,CAAC,WAAW,OAAO;QAC9E,IAAI,WAAW,QAAQ,CAAC,eAAe,WAAW,QAAQ,CAAC,SAAS,OAAO;QAE3E,OAAO;IACT;IAEQ,eAAe,KAAa,EAAuB;QACzD,MAAM,UAA+B,CAAC;QAEtC,MAAM,cAAc,MAAM,KAAK,CAAC;QAChC,IAAI,aAAa,QAAQ,MAAM,GAAG,WAAW,CAAC,EAAE;QAEhD,MAAM,gBAAgB,MAAM,KAAK,CAAC;QAClC,IAAI,eAAe,QAAQ,QAAQ,GAAG,aAAa,CAAC,EAAE;QAEtD,MAAM,gBAAgB,MAAM,KAAK,CAAC;QAClC,IAAI,eAAe,QAAQ,SAAS,GAAG,aAAa,CAAC,EAAE;QAEvD,MAAM,WAAW;YAAC;YAAa;YAAY;YAAU;YAAW;YAAY;SAAS;QACrF,MAAM,oBAAoB,SAAS,MAAM,CAAC,CAAA,UACxC,MAAM,WAAW,GAAG,QAAQ,CAAC;QAE/B,IAAI,kBAAkB,MAAM,GAAG,GAAG,QAAQ,QAAQ,GAAG;QAErD,OAAO;IACT;IAEQ,cAAc,KAAa,EAA6B;QAC9D,MAAM,QAAQ,MAAM,WAAW,GAAG,KAAK,CAAC;QACxC,MAAM,cAAc,MAAM,MAAM,CAAC,CAAA,OAAQ,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC;QAEpE,IAAI,YAAY,MAAM,GAAG,GAAG,OAAO;QACnC,IAAI,MAAM,WAAW,GAAG,QAAQ,CAAC,YAAY,MAAM,WAAW,GAAG,QAAQ,CAAC,YAAY,OAAO;QAC7F,OAAO;IACT;IAEQ,sBAAsB,KAAa,EAAU;QACnD,IAAI,QAAQ;QACZ,MAAM,WAAW;QAEjB,IAAI,MAAM,MAAM,GAAG,IAAI,SAAS;QAChC,IAAI,MAAM,MAAM,GAAG,KAAK,SAAS;QACjC,IAAI,MAAM,QAAQ,CAAC,SAAS,MAAM,WAAW,GAAG,QAAQ,CAAC,cAAc,SAAS;QAChF,IAAI,MAAM,KAAK,CAAC,oCAAoC,SAAS;QAC7D,IAAI,MAAM,WAAW,GAAG,QAAQ,CAAC,cAAc,MAAM,WAAW,GAAG,QAAQ,CAAC,WAAW,SAAS;QAChG,IAAI,MAAM,WAAW,GAAG,QAAQ,CAAC,cAAc,MAAM,WAAW,GAAG,QAAQ,CAAC,aAAa,SAAS;QAElG,OAAO,KAAK,GAAG,CAAC,QAAQ,UAAU;IACpC;IAEQ,iBAAiB,KAAa,EAAU;QAC9C,MAAM,qBAAqB;YACzB;YACA;SACD;QAED,OAAO,mBAAmB,MAAM,CAAC,CAAC,OAAO;YACvC,MAAM,UAAU,MAAM,KAAK,CAAC;YAC5B,OAAO,QAAQ,CAAC,UAAU,QAAQ,MAAM,GAAG,CAAC;QAC9C,GAAG;IACL;IAEQ,gBAAgB,KAAa,EAAqC;QACxE,MAAM,qBAAqB;YAAC;YAAM;YAAO;YAAU;YAAS;YAAY;SAAU;QAClF,MAAM,mBAAmB;YAAC;YAAW;YAAgB;YAAkB;YAAY;SAAW;QAE9F,MAAM,QAAQ,MAAM,WAAW,GAAG,KAAK,CAAC;QACxC,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA,OAAQ,mBAAmB,QAAQ,CAAC,OAAO,MAAM;QACpF,MAAM,cAAc,MAAM,MAAM,CAAC,CAAA,OAAQ,iBAAiB,QAAQ,CAAC,OAAO,MAAM;QAEhF,IAAI,cAAc,eAAe,OAAO;QACxC,IAAI,gBAAgB,aAAa,OAAO;QACxC,OAAO;IACT;IAEQ,0BAA0B,SAAmB,EAAE,KAAe,EAAU;QAC9E,IAAI,UAAU,MAAM,KAAK,KAAK,MAAM,MAAM,KAAK,GAAG,OAAO;QAEzD,MAAM,oBAAoB,MAAM,MAAM,GAAG,UAAU,MAAM;QACzD,MAAM,eAAe,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG,GAAG,MAAM;QACjE,MAAM,mBAAmB,eAAe,MAAM,MAAM;QAEpD,6DAA6D;QAC7D,OAAO,KAAK,GAAG,CAAC,GAAG,MAAO,oBAAoB,MAAQ,mBAAmB;IAC3E;IAnfA,aAAsB;QAnCtB,2CAA2C;QAC3C,+KAAiB,kBAAiB,IAAI,IAAI;YACxC;YAAY;YAAa;YAAe;YAAc;YAAY;YAClE;YAAO;YAAO;YAAO;YAAO;YAAc;YAAW;YACrD;YAAe;YAAa;YAAe;YAAa;YACxD;YAAa;YAAgB;YAAa;YAAY;SACvD;QAED,+KAAiB,iBAAgB,IAAI,IAAI;YACvC;YAAW;YAAW;YAAW;YAAW;YAAc;YAAO;YACjE;YAAW;YAAe;YAAS;YAAU;YAAgB;YAC7D;YAAW;YAAgB;YAAc;YAAS;SACnD;QAED,+KAAiB,kBAAiB,IAAI,IAAI;YACxC;YAAO;YAAO;YAAO;YAAa;YAAa;YAAO;YACtD;YAAa;YAAc;YAAc;YAAW;YACpD;YAAS;YAAO;YAAY;YAAe;SAC5C;QAED,+KAAiB,qBAAoB,IAAI,IAAI;YAC3C;YAAW;YAAY;YAAQ;YAAS;YAAM;YAAU;YACxD;YAAc;YAAW;YAAe;YAAS;SAClD;QAED,+KAAiB,sBAAqB,IAAI,IAAI;YAC5C;YAAS;YAAa;YAAY;YAAe;YACjD;YAAW;YAAY;YAAc;YAAS;YAAY;SAC3D;QAED,+KAAiB,sBAAqB,IAAI,IAAI;YAC5C;YAAY;YAAQ;YAAS;YAAQ;YAAW;YAChD;YAAc;YAAW;YAAY;YAAa;SACnD;IAEsB;AAofzB;AAzhBE,yKADW,mBACI,YAAf,KAAA", "debugId": null}}, {"offset": {"line": 1775, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent3/agentepub/src/lib/agents/knowledgeBase.ts"], "sourcesContent": ["/**\n * Agent Knowledge Base and Specialization System\n * \n * This module contains specialized knowledge, best practices, and response templates\n * for each agent type, enabling them to provide expert-level insights and recommendations.\n */\n\nimport { InputAnalysis, ResponseSection } from './responseEngine'\n\nexport interface AgentKnowledge {\n  specializations: string[]\n  commonScenarios: Scenario[]\n  bestPractices: BestPractice[]\n  troubleshooting: TroubleshootingGuide[]\n  templates: ResponseTemplate[]\n  industryInsights: InsightRule[]\n  warningTriggers: WarningTrigger[]\n}\n\nexport interface Scenario {\n  id: string\n  name: string\n  description: string\n  triggers: string[]\n  response: string\n  examples: string[]\n  relatedTopics: string[]\n}\n\nexport interface BestPractice {\n  category: string\n  title: string\n  description: string\n  implementation: string[]\n  benefits: string[]\n  commonMistakes: string[]\n}\n\nexport interface TroubleshootingGuide {\n  problem: string\n  symptoms: string[]\n  causes: string[]\n  solutions: string[]\n  prevention: string[]\n}\n\nexport interface ResponseTemplate {\n  name: string\n  structure: TemplateSection[]\n  applicableWhen: (analysis: InputAnalysis) => boolean\n}\n\nexport interface TemplateSection {\n  title: string\n  contentGenerator: (analysis: InputAnalysis, input: string) => string\n  priority: number\n  required: boolean\n}\n\nexport interface InsightRule {\n  condition: (analysis: InputAnalysis) => boolean\n  insight: string\n  actionable: boolean\n  priority: 'high' | 'medium' | 'low'\n}\n\nexport interface WarningTrigger {\n  condition: (analysis: InputAnalysis) => boolean\n  warning: string\n  severity: 'critical' | 'important' | 'advisory'\n  mitigation: string[]\n}\n\nexport class AgentKnowledgeBase {\n  private static instance: AgentKnowledgeBase\n  private knowledgeMap: Map<string, AgentKnowledge> = new Map()\n\n  private constructor() {\n    this.initializeKnowledge()\n  }\n\n  public static getInstance(): AgentKnowledgeBase {\n    if (!AgentKnowledgeBase.instance) {\n      AgentKnowledgeBase.instance = new AgentKnowledgeBase()\n    }\n    return AgentKnowledgeBase.instance\n  }\n\n  public getAgentKnowledge(agentId: string): AgentKnowledge | undefined {\n    return this.knowledgeMap.get(agentId)\n  }\n\n  public getApplicableScenarios(agentId: string, analysis: InputAnalysis): Scenario[] {\n    const knowledge = this.knowledgeMap.get(agentId)\n    if (!knowledge) return []\n\n    return knowledge.commonScenarios.filter(scenario =>\n      scenario.triggers.some(trigger =>\n        analysis.keywords.some(keyword => keyword.includes(trigger.toLowerCase())) ||\n        analysis.intent.includes(trigger.toLowerCase())\n      )\n    )\n  }\n\n  public getBestPractices(agentId: string, analysis: InputAnalysis): BestPractice[] {\n    const knowledge = this.knowledgeMap.get(agentId)\n    if (!knowledge) return []\n\n    // Return relevant best practices based on context\n    return knowledge.bestPractices.filter(practice =>\n      analysis.keywords.some(keyword => \n        practice.category.toLowerCase().includes(keyword) ||\n        practice.title.toLowerCase().includes(keyword)\n      )\n    ).slice(0, 3) // Limit to top 3 most relevant\n  }\n\n  public getInsights(agentId: string, analysis: InputAnalysis): InsightRule[] {\n    const knowledge = this.knowledgeMap.get(agentId)\n    if (!knowledge) return []\n\n    return knowledge.industryInsights\n      .filter(rule => rule.condition(analysis))\n      .sort((a, b) => {\n        const priorityOrder = { high: 3, medium: 2, low: 1 }\n        return priorityOrder[b.priority] - priorityOrder[a.priority]\n      })\n  }\n\n  public getWarnings(agentId: string, analysis: InputAnalysis): WarningTrigger[] {\n    const knowledge = this.knowledgeMap.get(agentId)\n    if (!knowledge) return []\n\n    return knowledge.warningTriggers.filter(trigger => trigger.condition(analysis))\n  }\n\n  public getTroubleshootingGuides(agentId: string, analysis: InputAnalysis): TroubleshootingGuide[] {\n    const knowledge = this.knowledgeMap.get(agentId)\n    if (!knowledge) return []\n\n    // Return guides that match problem indicators in the input\n    return knowledge.troubleshooting.filter(guide =>\n      guide.symptoms.some(symptom =>\n        analysis.keywords.some(keyword => keyword.includes(symptom.toLowerCase()))\n      )\n    )\n  }\n\n  private initializeKnowledge(): void {\n    // Initialize Atendimento Agent Knowledge\n    this.knowledgeMap.set('atendimento', {\n      specializations: [\n        'Gestão de Briefings',\n        'Comunicação com Cliente',\n        'Cronogramas de Aprovação',\n        'Gestão de Expectativas',\n        'Documentação de Projetos'\n      ],\n      commonScenarios: [\n        {\n          id: 'briefing_incompleto',\n          name: 'Briefing Incompleto',\n          description: 'Cliente forneceu informações insuficientes para iniciar o projeto',\n          triggers: ['briefing', 'incompleto', 'faltando', 'informação'],\n          response: 'Identifiquei que algumas informações essenciais estão faltando no briefing.',\n          examples: [\n            'Briefing sem definição de público-alvo',\n            'Orçamento não especificado',\n            'Prazo não definido'\n          ],\n          relatedTopics: ['Coleta de Informações', 'Validação de Briefing', 'Comunicação com Cliente']\n        },\n        {\n          id: 'prazo_apertado',\n          name: 'Prazo Apertado',\n          description: 'Cliente solicita entrega em prazo muito curto',\n          triggers: ['urgente', 'rápido', 'hoje', 'amanhã', 'prazo'],\n          response: 'Detectei uma solicitação com prazo apertado que requer atenção especial.',\n          examples: [\n            'Campanha para lançar em 48h',\n            'Material para evento na próxima semana',\n            'Aprovação urgente necessária'\n          ],\n          relatedTopics: ['Gestão de Cronograma', 'Priorização', 'Recursos Adicionais']\n        }\n      ],\n      bestPractices: [\n        {\n          category: 'Briefing',\n          title: 'Estruturação Completa de Briefings',\n          description: 'Todo briefing deve conter informações essenciais para evitar retrabalho',\n          implementation: [\n            'Sempre validar objetivo principal',\n            'Confirmar público-alvo detalhado',\n            'Estabelecer orçamento claro',\n            'Definir cronograma realista',\n            'Documentar canais de aprovação'\n          ],\n          benefits: [\n            'Reduz retrabalho em 60%',\n            'Melhora satisfação do cliente',\n            'Acelera processo de aprovação',\n            'Diminui mal-entendidos'\n          ],\n          commonMistakes: [\n            'Assumir informações não explícitas',\n            'Não validar orçamento real',\n            'Ignorar restrições do cliente',\n            'Não documentar mudanças'\n          ]\n        },\n        {\n          category: 'Comunicação',\n          title: 'Gestão Proativa de Expectativas',\n          description: 'Manter cliente informado previne problemas e builds confiança',\n          implementation: [\n            'Updates semanais obrigatórios',\n            'Alertas antecipados sobre riscos',\n            'Documentação de todas as decisões',\n            'Canal direto para dúvidas'\n          ],\n          benefits: [\n            'Aumenta confiança do cliente',\n            'Reduz ansiedade e pressão',\n            'Facilita resolução de problemas',\n            'Melhora relacionamento de longo prazo'\n          ],\n          commonMistakes: [\n            'Comunicar apenas quando há problemas',\n            'Usar linguagem muito técnica',\n            'Não confirmar entendimento',\n            'Deixar cliente sem resposta'\n          ]\n        }\n      ],\n      troubleshooting: [\n        {\n          problem: 'Cliente não responde aprovações',\n          symptoms: ['silêncio prolongado', 'aprovações pendentes', 'cronograma atrasado'],\n          causes: [\n            'Sobrecarga do cliente',\n            'Falta de clareza na solicitação',\n            'Processo de aprovação interno complexo',\n            'Insatisfação não comunicada'\n          ],\n          solutions: [\n            'Ligar diretamente para o cliente',\n            'Simplificar processo de aprovação',\n            'Oferecer reunião de alinhamento',\n            'Criar cronograma de follow-ups'\n          ],\n          prevention: [\n            'Estabelecer SLA de resposta',\n            'Mapear processo interno do cliente',\n            'Criar lembretes automáticos',\n            'Ter contato backup'\n          ]\n        }\n      ],\n      templates: [\n        {\n          name: 'Briefing Estruturado',\n          structure: [\n            {\n              title: '📋 Resumo Executivo',\n              contentGenerator: (analysis, input) => this.generateExecutiveSummary(analysis, input),\n              priority: 10,\n              required: true\n            },\n            {\n              title: '🎯 Objetivos e Metas',\n              contentGenerator: (analysis, input) => this.generateObjectives(analysis, input),\n              priority: 9,\n              required: true\n            },\n            {\n              title: '👥 Público-Alvo',\n              contentGenerator: (analysis, input) => this.generateTargetAudience(analysis, input),\n              priority: 8,\n              required: true\n            }\n          ],\n          applicableWhen: (analysis) => analysis.intent.includes('briefing') || analysis.keywords.includes('briefing')\n        }\n      ],\n      industryInsights: [\n        {\n          condition: (analysis) => analysis.context.budget && parseFloat(analysis.context.budget.replace(/[^\\d]/g, '')) < 10000,\n          insight: 'Para orçamentos menores, recomendo focar em 1-2 canais principais para maximizar impacto.',\n          actionable: true,\n          priority: 'high'\n        },\n        {\n          condition: (analysis) => analysis.urgency === 'high',\n          insight: 'Projetos urgentes têm 40% mais chance de problemas. Considere recursos adicionais.',\n          actionable: true,\n          priority: 'high'\n        }\n      ],\n      warningTriggers: [\n        {\n          condition: (analysis) => analysis.completeness < 0.5,\n          warning: 'Briefing incompleto detectado. Informações essenciais estão faltando.',\n          severity: 'important',\n          mitigation: [\n            'Solicitar reunião de alinhamento',\n            'Enviar checklist de informações necessárias',\n            'Agendar call de briefing completo'\n          ]\n        },\n        {\n          condition: (analysis) => analysis.urgency === 'high' && !analysis.context.budget,\n          warning: 'Projeto urgente sem orçamento definido pode gerar problemas de execução.',\n          severity: 'critical',\n          mitigation: [\n            'Definir orçamento mínimo imediatamente',\n            'Estabelecer escopo reduzido',\n            'Comunicar riscos ao cliente'\n          ]\n        }\n      ]\n    })\n\n    // Initialize Planejamento Agent Knowledge\n    this.knowledgeMap.set('planejamento', {\n      specializations: [\n        'Estratégia de Campanhas',\n        'Cronogramas Inteligentes',\n        'Análise de Concorrência',\n        'Distribuição de Budget',\n        'Otimização de Performance'\n      ],\n      commonScenarios: [\n        {\n          id: 'campanha_multicanal',\n          name: 'Campanha Multi-canal',\n          description: 'Planejamento de campanha que utiliza múltiplos canais de mídia',\n          triggers: ['multicanal', 'instagram', 'facebook', 'google', 'canais'],\n          response: 'Identificada necessidade de estratégia multi-canal integrada.',\n          examples: [\n            'Campanha usando Instagram + Google Ads',\n            'Estratégia omnichannel completa',\n            'Integração online e offline'\n          ],\n          relatedTopics: ['Integração de Canais', 'Attribution Modeling', 'Budget Allocation']\n        }\n      ],\n      bestPractices: [\n        {\n          category: 'Estratégia',\n          title: 'Planejamento Baseado em Dados',\n          description: 'Usar dados históricos e benchmarks para criar estratégias mais eficazes',\n          implementation: [\n            'Analisar performance histórica',\n            'Benchmarking com concorrentes',\n            'Definir KPIs específicos',\n            'Criar hipóteses testáveis'\n          ],\n          benefits: [\n            'Melhora ROI em até 35%',\n            'Reduz tempo de otimização',\n            'Aumenta previsibilidade',\n            'Facilita tomada de decisão'\n          ],\n          commonMistakes: [\n            'Ignorar dados históricos',\n            'Copiar estratégias sem contexto',\n            'Não definir métricas claras',\n            'Não testar hipóteses'\n          ]\n        }\n      ],\n      troubleshooting: [],\n      templates: [],\n      industryInsights: [],\n      warningTriggers: []\n    })\n\n    // Initialize Mídia Agent Knowledge\n    this.knowledgeMap.set('midia', {\n      specializations: [\n        'Análise de Performance',\n        'Otimização de Campanhas',\n        'Relatórios Avançados',\n        'Gestão de Budget',\n        'Targeting e Segmentação'\n      ],\n      commonScenarios: [],\n      bestPractices: [],\n      troubleshooting: [],\n      templates: [],\n      industryInsights: [],\n      warningTriggers: []\n    })\n  }\n\n  // Template content generators\n  private generateExecutiveSummary(analysis: InputAnalysis, input: string): string {\n    return `**Projeto:** ${analysis.context.campaign || 'Campanha identificada no briefing'}\n**Complexidade:** ${analysis.complexity}\n**Prazo estimado:** ${analysis.context.timeline || '30 dias (recomendado)'}\n**Status:** Briefing analisado e estruturado`\n  }\n\n  private generateObjectives(analysis: InputAnalysis, input: string): string {\n    const objectives = []\n    if (analysis.keywords.includes('lançamento')) objectives.push('• Lançamento de produto/serviço')\n    if (analysis.keywords.includes('awareness')) objectives.push('• Aumentar conhecimento da marca')\n    if (analysis.keywords.includes('vendas')) objectives.push('• Gerar vendas diretas')\n    if (objectives.length === 0) objectives.push('• Objetivo principal identificado no briefing')\n    \n    return objectives.join('\\n')\n  }\n\n  private generateTargetAudience(analysis: InputAnalysis, input: string): string {\n    let audience = '• **Idade:** '\n    if (analysis.context.targetAge) {\n      audience += analysis.context.targetAge\n    } else {\n      audience += '25-45 anos (sugerido)'\n    }\n    \n    audience += '\\n• **Localização:** Principais centros urbanos'\n    audience += '\\n• **Comportamento:** Ativo em redes sociais'\n    \n    return audience\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;;;AAoEM,MAAM;IAQX,OAAc,cAAkC;QAC9C,IAAI,CAAC,mBAAmB,QAAQ,EAAE;YAChC,mBAAmB,QAAQ,GAAG,IAAI;QACpC;QACA,OAAO,mBAAmB,QAAQ;IACpC;IAEO,kBAAkB,OAAe,EAA8B;QACpE,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC;IAC/B;IAEO,uBAAuB,OAAe,EAAE,QAAuB,EAAc;QAClF,MAAM,YAAY,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC;QACxC,IAAI,CAAC,WAAW,OAAO,EAAE;QAEzB,OAAO,UAAU,eAAe,CAAC,MAAM,CAAC,CAAA,WACtC,SAAS,QAAQ,CAAC,IAAI,CAAC,CAAA,UACrB,SAAS,QAAQ,CAAC,IAAI,CAAC,CAAA,UAAW,QAAQ,QAAQ,CAAC,QAAQ,WAAW,QACtE,SAAS,MAAM,CAAC,QAAQ,CAAC,QAAQ,WAAW;IAGlD;IAEO,iBAAiB,OAAe,EAAE,QAAuB,EAAkB;QAChF,MAAM,YAAY,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC;QACxC,IAAI,CAAC,WAAW,OAAO,EAAE;QAEzB,kDAAkD;QAClD,OAAO,UAAU,aAAa,CAAC,MAAM,CAAC,CAAA,WACpC,SAAS,QAAQ,CAAC,IAAI,CAAC,CAAA,UACrB,SAAS,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,YACzC,SAAS,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAExC,KAAK,CAAC,GAAG,GAAG,+BAA+B;;IAC/C;IAEO,YAAY,OAAe,EAAE,QAAuB,EAAiB;QAC1E,MAAM,YAAY,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC;QACxC,IAAI,CAAC,WAAW,OAAO,EAAE;QAEzB,OAAO,UAAU,gBAAgB,CAC9B,MAAM,CAAC,CAAA,OAAQ,KAAK,SAAS,CAAC,WAC9B,IAAI,CAAC,CAAC,GAAG;YACR,MAAM,gBAAgB;gBAAE,MAAM;gBAAG,QAAQ;gBAAG,KAAK;YAAE;YACnD,OAAO,aAAa,CAAC,EAAE,QAAQ,CAAC,GAAG,aAAa,CAAC,EAAE,QAAQ,CAAC;QAC9D;IACJ;IAEO,YAAY,OAAe,EAAE,QAAuB,EAAoB;QAC7E,MAAM,YAAY,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC;QACxC,IAAI,CAAC,WAAW,OAAO,EAAE;QAEzB,OAAO,UAAU,eAAe,CAAC,MAAM,CAAC,CAAA,UAAW,QAAQ,SAAS,CAAC;IACvE;IAEO,yBAAyB,OAAe,EAAE,QAAuB,EAA0B;QAChG,MAAM,YAAY,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC;QACxC,IAAI,CAAC,WAAW,OAAO,EAAE;QAEzB,2DAA2D;QAC3D,OAAO,UAAU,eAAe,CAAC,MAAM,CAAC,CAAA,QACtC,MAAM,QAAQ,CAAC,IAAI,CAAC,CAAA,UAClB,SAAS,QAAQ,CAAC,IAAI,CAAC,CAAA,UAAW,QAAQ,QAAQ,CAAC,QAAQ,WAAW;IAG5E;IAEQ,sBAA4B;QAClC,yCAAyC;QACzC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,eAAe;YACnC,iBAAiB;gBACf;gBACA;gBACA;gBACA;gBACA;aACD;YACD,iBAAiB;gBACf;oBACE,IAAI;oBACJ,MAAM;oBACN,aAAa;oBACb,UAAU;wBAAC;wBAAY;wBAAc;wBAAY;qBAAa;oBAC9D,UAAU;oBACV,UAAU;wBACR;wBACA;wBACA;qBACD;oBACD,eAAe;wBAAC;wBAAyB;wBAAyB;qBAA0B;gBAC9F;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,aAAa;oBACb,UAAU;wBAAC;wBAAW;wBAAU;wBAAQ;wBAAU;qBAAQ;oBAC1D,UAAU;oBACV,UAAU;wBACR;wBACA;wBACA;qBACD;oBACD,eAAe;wBAAC;wBAAwB;wBAAe;qBAAsB;gBAC/E;aACD;YACD,eAAe;gBACb;oBACE,UAAU;oBACV,OAAO;oBACP,aAAa;oBACb,gBAAgB;wBACd;wBACA;wBACA;wBACA;wBACA;qBACD;oBACD,UAAU;wBACR;wBACA;wBACA;wBACA;qBACD;oBACD,gBAAgB;wBACd;wBACA;wBACA;wBACA;qBACD;gBACH;gBACA;oBACE,UAAU;oBACV,OAAO;oBACP,aAAa;oBACb,gBAAgB;wBACd;wBACA;wBACA;wBACA;qBACD;oBACD,UAAU;wBACR;wBACA;wBACA;wBACA;qBACD;oBACD,gBAAgB;wBACd;wBACA;wBACA;wBACA;qBACD;gBACH;aACD;YACD,iBAAiB;gBACf;oBACE,SAAS;oBACT,UAAU;wBAAC;wBAAuB;wBAAwB;qBAAsB;oBAChF,QAAQ;wBACN;wBACA;wBACA;wBACA;qBACD;oBACD,WAAW;wBACT;wBACA;wBACA;wBACA;qBACD;oBACD,YAAY;wBACV;wBACA;wBACA;wBACA;qBACD;gBACH;aACD;YACD,WAAW;gBACT;oBACE,MAAM;oBACN,WAAW;wBACT;4BACE,OAAO;4BACP,kBAAkB,CAAC,UAAU,QAAU,IAAI,CAAC,wBAAwB,CAAC,UAAU;4BAC/E,UAAU;4BACV,UAAU;wBACZ;wBACA;4BACE,OAAO;4BACP,kBAAkB,CAAC,UAAU,QAAU,IAAI,CAAC,kBAAkB,CAAC,UAAU;4BACzE,UAAU;4BACV,UAAU;wBACZ;wBACA;4BACE,OAAO;4BACP,kBAAkB,CAAC,UAAU,QAAU,IAAI,CAAC,sBAAsB,CAAC,UAAU;4BAC7E,UAAU;4BACV,UAAU;wBACZ;qBACD;oBACD,gBAAgB,CAAC,WAAa,SAAS,MAAM,CAAC,QAAQ,CAAC,eAAe,SAAS,QAAQ,CAAC,QAAQ,CAAC;gBACnG;aACD;YACD,kBAAkB;gBAChB;oBACE,WAAW,CAAC,WAAa,SAAS,OAAO,CAAC,MAAM,IAAI,WAAW,SAAS,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,OAAO;oBAChH,SAAS;oBACT,YAAY;oBACZ,UAAU;gBACZ;gBACA;oBACE,WAAW,CAAC,WAAa,SAAS,OAAO,KAAK;oBAC9C,SAAS;oBACT,YAAY;oBACZ,UAAU;gBACZ;aACD;YACD,iBAAiB;gBACf;oBACE,WAAW,CAAC,WAAa,SAAS,YAAY,GAAG;oBACjD,SAAS;oBACT,UAAU;oBACV,YAAY;wBACV;wBACA;wBACA;qBACD;gBACH;gBACA;oBACE,WAAW,CAAC,WAAa,SAAS,OAAO,KAAK,UAAU,CAAC,SAAS,OAAO,CAAC,MAAM;oBAChF,SAAS;oBACT,UAAU;oBACV,YAAY;wBACV;wBACA;wBACA;qBACD;gBACH;aACD;QACH;QAEA,0CAA0C;QAC1C,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,gBAAgB;YACpC,iBAAiB;gBACf;gBACA;gBACA;gBACA;gBACA;aACD;YACD,iBAAiB;gBACf;oBACE,IAAI;oBACJ,MAAM;oBACN,aAAa;oBACb,UAAU;wBAAC;wBAAc;wBAAa;wBAAY;wBAAU;qBAAS;oBACrE,UAAU;oBACV,UAAU;wBACR;wBACA;wBACA;qBACD;oBACD,eAAe;wBAAC;wBAAwB;wBAAwB;qBAAoB;gBACtF;aACD;YACD,eAAe;gBACb;oBACE,UAAU;oBACV,OAAO;oBACP,aAAa;oBACb,gBAAgB;wBACd;wBACA;wBACA;wBACA;qBACD;oBACD,UAAU;wBACR;wBACA;wBACA;wBACA;qBACD;oBACD,gBAAgB;wBACd;wBACA;wBACA;wBACA;qBACD;gBACH;aACD;YACD,iBAAiB,EAAE;YACnB,WAAW,EAAE;YACb,kBAAkB,EAAE;YACpB,iBAAiB,EAAE;QACrB;QAEA,mCAAmC;QACnC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS;YAC7B,iBAAiB;gBACf;gBACA;gBACA;gBACA;gBACA;aACD;YACD,iBAAiB,EAAE;YACnB,eAAe,EAAE;YACjB,iBAAiB,EAAE;YACnB,WAAW,EAAE;YACb,kBAAkB,EAAE;YACpB,iBAAiB,EAAE;QACrB;IACF;IAEA,8BAA8B;IACtB,yBAAyB,QAAuB,EAAE,KAAa,EAAU;QAC/E,OAAO,AAAC,gBACQ,OADO,SAAS,OAAO,CAAC,QAAQ,IAAI,qCAAoC,wBAEtE,OADF,SAAS,UAAU,EAAC,0BACmC,OAArD,SAAS,OAAO,CAAC,QAAQ,IAAI,yBAAwB;IAEzE;IAEQ,mBAAmB,QAAuB,EAAE,KAAa,EAAU;QACzE,MAAM,aAAa,EAAE;QACrB,IAAI,SAAS,QAAQ,CAAC,QAAQ,CAAC,eAAe,WAAW,IAAI,CAAC;QAC9D,IAAI,SAAS,QAAQ,CAAC,QAAQ,CAAC,cAAc,WAAW,IAAI,CAAC;QAC7D,IAAI,SAAS,QAAQ,CAAC,QAAQ,CAAC,WAAW,WAAW,IAAI,CAAC;QAC1D,IAAI,WAAW,MAAM,KAAK,GAAG,WAAW,IAAI,CAAC;QAE7C,OAAO,WAAW,IAAI,CAAC;IACzB;IAEQ,uBAAuB,QAAuB,EAAE,KAAa,EAAU;QAC7E,IAAI,WAAW;QACf,IAAI,SAAS,OAAO,CAAC,SAAS,EAAE;YAC9B,YAAY,SAAS,OAAO,CAAC,SAAS;QACxC,OAAO;YACL,YAAY;QACd;QAEA,YAAY;QACZ,YAAY;QAEZ,OAAO;IACT;IA7VA,aAAsB;QAFtB,+KAAQ,gBAA4C,IAAI;QAGtD,IAAI,CAAC,mBAAmB;IAC1B;AA4VF;AAjWE,yKADW,oBACI,YAAf,KAAA", "debugId": null}}, {"offset": {"line": 2145, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent3/agentepub/src/lib/agents/responseTemplates.ts"], "sourcesContent": ["/**\n * Context-Aware Response Templates System\n *\n * This module provides dynamic response templates that adapt based on input analysis,\n * user context, and agent specialization to generate comprehensive, structured responses.\n */\n\nimport { InputAnalysis, ResponseSection } from './responseEngine'\nimport { AgentKnowledgeBase } from './knowledgeBase'\n\nexport interface TemplateContext {\n  agentId: string\n  analysis: InputAnalysis\n  input: string\n  userContext?: Record<string, any>\n}\n\nexport interface DynamicTemplate {\n  id: string\n  name: string\n  description: string\n  applicableWhen: (context: TemplateContext) => boolean\n  priority: number\n  sections: TemplateSectionGenerator[]\n}\n\nexport interface TemplateSectionGenerator {\n  id: string\n  title: string\n  type: 'analysis' | 'recommendation' | 'example' | 'warning' | 'next-steps' | 'best-practices' | 'detailed-guide'\n  priority: number\n  required: boolean\n  condition?: (context: TemplateContext) => boolean\n  generator: (context: TemplateContext) => ResponseSection\n}\n\nexport class ResponseTemplateEngine {\n  private static instance: ResponseTemplateEngine\n  private templates: Map<string, DynamicTemplate[]> = new Map()\n  private knowledgeBase: AgentKnowledgeBase\n\n  private constructor() {\n    this.knowledgeBase = AgentKnowledgeBase.getInstance()\n    this.initializeTemplates()\n  }\n\n  public static getInstance(): ResponseTemplateEngine {\n    if (!ResponseTemplateEngine.instance) {\n      ResponseTemplateEngine.instance = new ResponseTemplateEngine()\n    }\n    return ResponseTemplateEngine.instance\n  }\n\n  public generateResponse(context: TemplateContext): ResponseSection[] {\n    const agentTemplates = this.templates.get(context.agentId) || []\n\n    // Find applicable templates\n    const applicableTemplates = agentTemplates\n      .filter(template => template.applicableWhen(context))\n      .sort((a, b) => b.priority - a.priority)\n\n    // Use the highest priority applicable template\n    const selectedTemplate = applicableTemplates[0]\n    if (!selectedTemplate) {\n      return this.generateFallbackResponse(context)\n    }\n\n    // Generate sections from template\n    const sections: ResponseSection[] = []\n\n    for (const sectionGen of selectedTemplate.sections) {\n      // Check if section condition is met (if any)\n      if (sectionGen.condition && !sectionGen.condition(context)) {\n        continue\n      }\n\n      try {\n        const section = sectionGen.generator(context)\n        sections.push(section)\n      } catch (error) {\n        console.error(`Error generating section ${sectionGen.id}:`, error)\n        // Continue with other sections\n      }\n    }\n\n    // Sort sections by priority\n    return sections.sort((a, b) => b.priority - a.priority)\n  }\n\n  private initializeTemplates(): void {\n    // Atendimento Agent Templates\n    this.templates.set('atendimento', [\n      {\n        id: 'comprehensive_briefing',\n        name: 'Briefing Abrangente',\n        description: 'Template completo para análise e estruturação de briefings',\n        applicableWhen: (context) =>\n          context.analysis.intent.includes('briefing') ||\n          context.analysis.keywords.includes('briefing') ||\n          context.analysis.intent === 'create_briefing',\n        priority: 10,\n        sections: [\n          {\n            id: 'executive_summary',\n            title: '📋 Resumo Executivo',\n            type: 'analysis',\n            priority: 10,\n            required: true,\n            generator: (context) => this.generateExecutiveSummary(context)\n          },\n          {\n            id: 'detailed_analysis',\n            title: '🔍 Análise Detalhada do Briefing',\n            type: 'analysis',\n            priority: 9,\n            required: true,\n            generator: (context) => this.generateDetailedAnalysis(context)\n          },\n          {\n            id: 'missing_information',\n            title: '❓ Informações Necessárias',\n            type: 'warning',\n            priority: 8,\n            required: false,\n            condition: (context) => context.analysis.completeness < 0.7,\n            generator: (context) => this.generateMissingInformation(context)\n          },\n          {\n            id: 'strategic_recommendations',\n            title: '💡 Recomendações Estratégicas',\n            type: 'recommendation',\n            priority: 8,\n            required: true,\n            generator: (context) => this.generateStrategicRecommendations(context)\n          },\n          {\n            id: 'timeline_breakdown',\n            title: '📅 Cronograma Detalhado',\n            type: 'detailed-guide',\n            priority: 7,\n            required: true,\n            generator: (context) => this.generateTimelineBreakdown(context)\n          },\n          {\n            id: 'approval_process',\n            title: '✅ Processo de Aprovação',\n            type: 'detailed-guide',\n            priority: 6,\n            required: true,\n            generator: (context) => this.generateApprovalProcess(context)\n          },\n          {\n            id: 'risk_assessment',\n            title: '⚠️ Análise de Riscos',\n            type: 'warning',\n            priority: 7,\n            required: false,\n            condition: (context) => context.analysis.urgency === 'high' || context.analysis.complexity === 'advanced',\n            generator: (context) => this.generateRiskAssessment(context)\n          },\n          {\n            id: 'best_practices',\n            title: '🎯 Melhores Práticas',\n            type: 'best-practices',\n            priority: 5,\n            required: false,\n            generator: (context) => this.generateBestPractices(context)\n          },\n          {\n            id: 'next_steps',\n            title: '🚀 Próximos Passos',\n            type: 'next-steps',\n            priority: 9,\n            required: true,\n            generator: (context) => this.generateNextSteps(context)\n          }\n        ]\n      },\n      {\n        id: 'client_communication',\n        name: 'Comunicação com Cliente',\n        description: 'Template para situações de comunicação e alinhamento com cliente',\n        applicableWhen: (context) =>\n          context.analysis.keywords.some(k => ['cliente', 'comunicação', 'alinhamento', 'reunião'].includes(k)),\n        priority: 8,\n        sections: [\n          {\n            id: 'communication_analysis',\n            title: '📞 Análise da Situação',\n            type: 'analysis',\n            priority: 10,\n            required: true,\n            generator: (context) => this.generateCommunicationAnalysis(context)\n          },\n          {\n            id: 'communication_strategy',\n            title: '💬 Estratégia de Comunicação',\n            type: 'recommendation',\n            priority: 9,\n            required: true,\n            generator: (context) => this.generateCommunicationStrategy(context)\n          },\n          {\n            id: 'meeting_agenda',\n            title: '📋 Agenda de Reunião Sugerida',\n            type: 'detailed-guide',\n            priority: 7,\n            required: false,\n            condition: (context) => context.analysis.keywords.includes('reunião'),\n            generator: (context) => this.generateMeetingAgenda(context)\n          }\n        ]\n      }\n    ])\n\n    // Planejamento Agent Templates\n    this.templates.set('planejamento', [\n      {\n        id: 'strategic_planning',\n        name: 'Planejamento Estratégico',\n        description: 'Template completo para planejamento de campanhas',\n        applicableWhen: (context) =>\n          context.analysis.intent.includes('plan') ||\n          context.analysis.keywords.includes('planejamento') ||\n          context.analysis.keywords.includes('estratégia'),\n        priority: 10,\n        sections: [\n          {\n            id: 'strategic_overview',\n            title: '🎯 Visão Estratégica',\n            type: 'analysis',\n            priority: 10,\n            required: true,\n            generator: (context) => this.generateStrategicOverview(context)\n          },\n          {\n            id: 'market_analysis',\n            title: '📊 Análise de Mercado',\n            type: 'analysis',\n            priority: 9,\n            required: true,\n            generator: (context) => this.generateMarketAnalysis(context)\n          },\n          {\n            id: 'campaign_phases',\n            title: '📅 Fases da Campanha',\n            type: 'detailed-guide',\n            priority: 8,\n            required: true,\n            generator: (context) => this.generateCampaignPhases(context)\n          },\n          {\n            id: 'budget_allocation',\n            title: '💰 Distribuição de Budget',\n            type: 'detailed-guide',\n            priority: 8,\n            required: true,\n            generator: (context) => this.generateBudgetAllocation(context)\n          },\n          {\n            id: 'kpi_framework',\n            title: '📈 Framework de KPIs',\n            type: 'detailed-guide',\n            priority: 7,\n            required: true,\n            generator: (context) => this.generateKPIFramework(context)\n          }\n        ]\n      }\n    ])\n\n    // Mídia Agent Templates\n    this.templates.set('midia', [\n      {\n        id: 'performance_analysis',\n        name: 'Análise de Performance',\n        description: 'Template para análise detalhada de performance de campanhas',\n        applicableWhen: (context) =>\n          context.analysis.intent.includes('analyze') ||\n          context.analysis.keywords.some(k => ['performance', 'resultado', 'análise', 'métricas'].includes(k)),\n        priority: 10,\n        sections: [\n          {\n            id: 'performance_overview',\n            title: '📊 Visão Geral da Performance',\n            type: 'analysis',\n            priority: 10,\n            required: true,\n            generator: (context) => this.generatePerformanceOverview(context)\n          },\n          {\n            id: 'detailed_metrics',\n            title: '📈 Métricas Detalhadas',\n            type: 'analysis',\n            priority: 9,\n            required: true,\n            generator: (context) => this.generateDetailedMetrics(context)\n          },\n          {\n            id: 'optimization_opportunities',\n            title: '🎯 Oportunidades de Otimização',\n            type: 'recommendation',\n            priority: 8,\n            required: true,\n            generator: (context) => this.generateOptimizationOpportunities(context)\n          },\n          {\n            id: 'competitive_insights',\n            title: '🏆 Insights Competitivos',\n            type: 'analysis',\n            priority: 7,\n            required: false,\n            generator: (context) => this.generateCompetitiveInsights(context)\n          }\n        ]\n      }\n    ])\n  }\n\n  // Section generators implementation\n  private generateExecutiveSummary(context: TemplateContext): ResponseSection {\n    const { analysis, input } = context\n\n    let content = `**Status:** Briefing analisado com ${Math.round(analysis.completeness * 100)}% de completude\\n`\n    content += `**Complexidade:** ${this.translateComplexity(analysis.complexity)}\\n`\n    content += `**Urgência:** ${this.translateUrgency(analysis.urgency)}\\n`\n    content += `**Intenção Principal:** ${this.translateIntent(analysis.intent)}\\n\\n`\n\n    if (analysis.context.budget) {\n      content += `**Budget Identificado:** R$ ${analysis.context.budget}\\n`\n    }\n    if (analysis.context.timeline) {\n      content += `**Prazo:** ${analysis.context.timeline}\\n`\n    }\n    if (analysis.context.channels && analysis.context.channels.length > 0) {\n      content += `**Canais Mencionados:** ${analysis.context.channels.join(', ')}\\n`\n    }\n\n    return {\n      title: '📋 Resumo Executivo',\n      content,\n      type: 'analysis',\n      priority: 10,\n      metadata: { confidence: analysis.completeness }\n    }\n  }\n\n  private generateDetailedAnalysis(context: TemplateContext): ResponseSection {\n    const { analysis, input } = context\n\n    let content = '### Elementos Identificados\\n\\n'\n\n    if (analysis.keywords.length > 0) {\n      content += `**Palavras-chave principais:** ${analysis.keywords.slice(0, 5).join(', ')}\\n\\n`\n    }\n\n    if (analysis.entities.length > 0) {\n      content += `**Entidades detectadas:** ${analysis.entities.join(', ')}\\n\\n`\n    }\n\n    content += '### Contexto do Projeto\\n\\n'\n\n    if (Object.keys(analysis.context).length > 0) {\n      for (const [key, value] of Object.entries(analysis.context)) {\n        content += `• **${this.formatContextKey(key)}:** ${value}\\n`\n      }\n    } else {\n      content += '• Contexto adicional será coletado durante o processo de briefing\\n'\n    }\n\n    content += '\\n### Análise de Sentimento\\n\\n'\n    content += `O tom geral da solicitação é **${this.translateSentiment(analysis.sentiment)}**, `\n\n    switch (analysis.sentiment) {\n      case 'positive':\n        content += 'indicando confiança e expectativas positivas para o projeto.'\n        break\n      case 'negative':\n        content += 'sugerindo possíveis preocupações ou urgência que devem ser endereçadas.'\n        break\n      default:\n        content += 'mantendo um tom profissional e objetivo.'\n    }\n\n    return {\n      title: '🔍 Análise Detalhada do Briefing',\n      content,\n      type: 'analysis',\n      priority: 9\n    }\n  }\n\n  private generateMissingInformation(context: TemplateContext): ResponseSection {\n    const { analysis } = context\n    const missing: string[] = []\n\n    if (!analysis.context.budget) missing.push('Orçamento disponível')\n    if (!analysis.context.timeline) missing.push('Cronograma desejado')\n    if (!analysis.context.targetAge) missing.push('Público-alvo detalhado')\n    if (!analysis.context.channels) missing.push('Canais preferenciais')\n\n    let content = '### Informações Essenciais Faltantes\\n\\n'\n\n    if (missing.length > 0) {\n      missing.forEach(item => {\n        content += `• ${item}\\n`\n      })\n\n      content += '\\n### Impacto da Falta de Informações\\n\\n'\n      content += '• **Planejamento:** Pode resultar em cronograma impreciso\\n'\n      content += '• **Orçamento:** Dificulta estimativa de custos e ROI\\n'\n      content += '• **Estratégia:** Limita precisão das recomendações\\n'\n      content += '• **Execução:** Pode causar retrabalho e atrasos\\n'\n\n      content += '\\n### Recomendação\\n\\n'\n      content += 'Agendar reunião de alinhamento para coletar informações faltantes antes de prosseguir com o planejamento detalhado.'\n    } else {\n      content += 'Todas as informações essenciais foram fornecidas. Excelente briefing!'\n    }\n\n    return {\n      title: '❓ Informações Necessárias',\n      content,\n      type: 'warning',\n      priority: 8\n    }\n  }\n\n  private generateStrategicRecommendations(context: TemplateContext): ResponseSection {\n    const { analysis, input } = context\n    const knowledge = this.knowledgeBase.getAgentKnowledge(context.agentId)\n    const insights = this.knowledgeBase.getInsights(context.agentId, analysis)\n\n    let content = '### Recomendações Baseadas na Análise\\n\\n'\n\n    // Add insights from knowledge base\n    insights.forEach((insight, index) => {\n      content += `${index + 1}. **${insight.priority.toUpperCase()}:** ${insight.insight}\\n\\n`\n    })\n\n    // Add specific recommendations based on analysis\n    if (analysis.urgency === 'high') {\n      content += '• **Recursos Adicionais:** Considere alocar recursos extras para garantir qualidade\\n'\n      content += '• **Comunicação Intensiva:** Estabeleça check-ins diários com o cliente\\n'\n      content += '• **Cronograma Acelerado:** Defina marcos intermediários para controle\\n\\n'\n    }\n\n    if (analysis.complexity === 'advanced') {\n      content += '• **Especialistas:** Envolver especialistas desde o início do projeto\\n'\n      content += '• **Faseamento:** Dividir projeto em fases menores para melhor controle\\n'\n      content += '• **Documentação:** Manter registro detalhado de todas as decisões\\n\\n'\n    }\n\n    // Budget-based recommendations\n    if (analysis.context.budget) {\n      const budgetValue = parseFloat(analysis.context.budget.replace(/[^\\d]/g, ''))\n      if (budgetValue < 10000) {\n        content += '• **Foco Estratégico:** Com orçamento limitado, concentre em 1-2 canais principais\\n'\n        content += '• **ROI Máximo:** Priorize ações com maior potencial de retorno\\n\\n'\n      } else if (budgetValue > 100000) {\n        content += '• **Diversificação:** Budget permite estratégia multi-canal robusta\\n'\n        content += '• **Testes A/B:** Invista em testes para otimização contínua\\n\\n'\n      }\n    }\n\n    // Channel-specific recommendations\n    if (analysis.context.channels) {\n      content += '• **Integração de Canais:** Garanta mensagem consistente entre ' + analysis.context.channels.join(', ') + '\\n'\n      content += '• **Attribution:** Configure tracking para medir performance de cada canal\\n\\n'\n    }\n\n    // Default recommendations if no specific insights\n    if (insights.length === 0 && analysis.urgency === 'low' && analysis.complexity === 'basic') {\n      content += '• **Planejamento Estruturado:** Defina objetivos claros e mensuráveis\\n'\n      content += '• **Cronograma Realista:** Estabeleça prazos factíveis para cada etapa\\n'\n      content += '• **Comunicação Regular:** Mantenha cliente informado sobre progresso\\n'\n      content += '• **Métricas de Sucesso:** Defina KPIs específicos para acompanhamento\\n'\n    }\n\n    return {\n      title: '💡 Recomendações Estratégicas',\n      content,\n      type: 'recommendation',\n      priority: 8\n    }\n  }\n\n  private generateNextSteps(context: TemplateContext): ResponseSection {\n    const { analysis, input, agentId } = context\n\n    let content = '### Ações Imediatas (24-48h)\\n\\n'\n\n    // Agent-specific immediate actions\n    if (agentId === 'atendimento') {\n      content += '1. ✅ Validar briefing com cliente\\n'\n      if (analysis.completeness < 0.7) {\n        content += '2. 📋 Solicitar informações faltantes (ver seção de informações necessárias)\\n'\n      } else {\n        content += '2. 📋 Confirmar detalhes do briefing\\n'\n      }\n      content += '3. 👥 Definir equipe do projeto e responsabilidades\\n'\n      content += '4. 📅 Estabelecer cronograma de aprovações\\n'\n    } else if (agentId === 'planejamento') {\n      content += '1. 📊 Analisar dados históricos e benchmarks\\n'\n      content += '2. 🎯 Definir personas detalhadas\\n'\n      content += '3. 📈 Estabelecer KPIs e métricas de sucesso\\n'\n      content += '4. 💰 Validar distribuição de budget\\n'\n    } else if (agentId === 'midia') {\n      content += '1. 📊 Configurar tracking e analytics\\n'\n      content += '2. 🎯 Definir segmentações iniciais\\n'\n      content += '3. 💡 Criar primeiros criativos para teste\\n'\n      content += '4. 📈 Estabelecer baseline de performance\\n'\n    }\n\n    if (analysis.urgency === 'high') {\n      content += '5. 🚨 Ativar protocolo de urgência (recursos extras, comunicação diária)\\n'\n    }\n\n    content += '\\n### Próxima Semana\\n\\n'\n\n    // Week 1 actions based on agent and context\n    if (analysis.context.budget) {\n      const budgetValue = parseFloat(analysis.context.budget.replace(/[^\\d]/g, ''))\n      if (budgetValue > 50000) {\n        content += '• Desenvolver estratégia multi-canal detalhada\\n'\n        content += '• Planejar testes A/B para otimização\\n'\n      } else {\n        content += '• Focar estratégia em canais de maior ROI\\n'\n        content += '• Otimizar budget para máximo impacto\\n'\n      }\n    } else {\n      content += '• Desenvolver estratégia baseada em objetivos\\n'\n    }\n\n    content += '• Criar cronograma executivo detalhado\\n'\n    content += '• Definir marcos de aprovação e entrega\\n'\n\n    if (agentId === 'atendimento') {\n      content += '• Preparar primeira apresentação para cliente\\n'\n      content += '• Estabelecer canais de comunicação\\n'\n    } else if (agentId === 'planejamento') {\n      content += '• Finalizar estratégia e táticas\\n'\n      content += '• Preparar briefings para equipe criativa\\n'\n    } else if (agentId === 'midia') {\n      content += '• Configurar campanhas iniciais\\n'\n      content += '• Preparar dashboards de monitoramento\\n'\n    }\n\n    content += '\\n### Próximas 2-4 Semanas\\n\\n'\n    content += '• Implementar estratégia aprovada\\n'\n    content += '• Monitorar performance inicial\\n'\n    content += '• Ajustar táticas baseado em dados\\n'\n    content += '• Preparar relatórios de progresso\\n'\n\n    content += '\\n### Acompanhamento\\n\\n'\n    if (analysis.urgency === 'high') {\n      content += '• **Check-ins:** Diários até estabilização\\n'\n      content += '• **Relatórios:** Status reports a cada 3 dias\\n'\n    } else {\n      content += '• **Check-ins:** Semanais com cliente\\n'\n      content += '• **Relatórios:** Status reports quinzenais\\n'\n    }\n    content += '• **Revisões:** Marcos de aprovação pré-definidos\\n'\n    content += '• **Otimização:** Ajustes baseados em performance\\n'\n\n    return {\n      title: '🚀 Próximos Passos',\n      content,\n      type: 'next-steps',\n      priority: 9\n    }\n  }\n\n  private generateTimelineBreakdown(context: TemplateContext): ResponseSection {\n    const { analysis, agentId } = context\n\n    let content = '### Cronograma Sugerido\\n\\n'\n\n    // Determine timeline based on context\n    let totalWeeks = 4 // default\n    if (analysis.context.timeline) {\n      const timelineText = analysis.context.timeline.toLowerCase()\n      if (timelineText.includes('dia')) {\n        const days = parseInt(timelineText.match(/\\d+/)?.[0] || '30')\n        totalWeeks = Math.ceil(days / 7)\n      } else if (timelineText.includes('semana')) {\n        totalWeeks = parseInt(timelineText.match(/\\d+/)?.[0] || '4')\n      } else if (timelineText.includes('mes')) {\n        const months = parseInt(timelineText.match(/\\d+/)?.[0] || '1')\n        totalWeeks = months * 4\n      }\n    }\n\n    if (analysis.urgency === 'high') {\n      totalWeeks = Math.max(2, Math.ceil(totalWeeks * 0.7)) // Reduce by 30% for urgent projects\n    }\n\n    // Generate phase-based timeline\n    if (totalWeeks <= 2) {\n      content += '**⚡ Cronograma Acelerado (2 semanas)**\\n\\n'\n      content += '**Semana 1:**\\n'\n      content += '• Dias 1-2: Briefing e planejamento\\n'\n      content += '• Dias 3-5: Desenvolvimento e produção\\n'\n      content += '• Dias 6-7: Revisões e ajustes\\n\\n'\n      content += '**Semana 2:**\\n'\n      content += '• Dias 8-10: Implementação\\n'\n      content += '• Dias 11-12: Testes e otimização\\n'\n      content += '• Dias 13-14: Lançamento e monitoramento\\n'\n    } else if (totalWeeks <= 4) {\n      content += '**📅 Cronograma Padrão (4 semanas)**\\n\\n'\n      content += '**Semana 1 - Planejamento:**\\n'\n      content += '• Validação de briefing e objetivos\\n'\n      content += '• Pesquisa de mercado e concorrência\\n'\n      content += '• Definição de estratégia e táticas\\n'\n      content += '• Aprovação de conceitos iniciais\\n\\n'\n      content += '**Semana 2 - Desenvolvimento:**\\n'\n      content += '• Criação de materiais e conteúdo\\n'\n      content += '• Desenvolvimento de criativos\\n'\n      content += '• Setup de campanhas e tracking\\n'\n      content += '• Testes internos e ajustes\\n\\n'\n      content += '**Semana 3 - Implementação:**\\n'\n      content += '• Lançamento das campanhas\\n'\n      content += '• Monitoramento inicial\\n'\n      content += '• Primeiros ajustes baseados em dados\\n'\n      content += '• Relatório de performance inicial\\n\\n'\n      content += '**Semana 4 - Otimização:**\\n'\n      content += '• Análise de resultados\\n'\n      content += '• Otimizações baseadas em performance\\n'\n      content += '• Relatório final e recomendações\\n'\n      content += '• Planejamento de continuidade\\n'\n    } else {\n      content += `**📈 Cronograma Estendido (${totalWeeks} semanas)**\\n\\n`\n      const phaseDuration = Math.ceil(totalWeeks / 4)\n      content += `**Fase 1 - Planejamento (${phaseDuration} semanas):**\\n`\n      content += '• Pesquisa aprofundada e análise de mercado\\n'\n      content += '• Desenvolvimento de estratégia detalhada\\n'\n      content += '• Criação de personas e jornada do cliente\\n'\n      content += '• Aprovação de conceitos e direcionamento\\n\\n'\n      content += `**Fase 2 - Desenvolvimento (${phaseDuration} semanas):**\\n`\n      content += '• Produção de materiais e conteúdo\\n'\n      content += '• Desenvolvimento de criativos variados\\n'\n      content += '• Setup completo de campanhas\\n'\n      content += '• Testes A/B e validações\\n\\n'\n      content += `**Fase 3 - Lançamento (${phaseDuration} semanas):**\\n`\n      content += '• Lançamento faseado das campanhas\\n'\n      content += '• Monitoramento intensivo\\n'\n      content += '• Otimizações contínuas\\n'\n      content += '• Expansão baseada em resultados\\n\\n'\n      content += `**Fase 4 - Consolidação (${totalWeeks - (phaseDuration * 3)} semanas):**\\n`\n      content += '• Análise completa de resultados\\n'\n      content += '• Documentação de aprendizados\\n'\n      content += '• Recomendações para continuidade\\n'\n      content += '• Planejamento de próximas fases\\n'\n    }\n\n    content += '\\n### Marcos Críticos\\n\\n'\n    content += '🎯 **Aprovação de Estratégia:** Fim da primeira fase\\n'\n    content += '🎨 **Aprovação de Criativos:** Meio do desenvolvimento\\n'\n    content += '🚀 **Go-Live:** Início da implementação\\n'\n    content += '📊 **Primeira Análise:** 1 semana após lançamento\\n'\n\n    return {\n      title: '📅 Cronograma Detalhado',\n      content,\n      type: 'detailed-guide',\n      priority: 7\n    }\n  }\n\n  private generateApprovalProcess(context: TemplateContext): ResponseSection {\n    const { analysis, agentId } = context\n\n    let content = '### Fluxo de Aprovações\\n\\n'\n\n    content += '**1. Aprovação de Estratégia**\\n'\n    content += '• Apresentação de briefing estruturado\\n'\n    content += '• Validação de objetivos e KPIs\\n'\n    content += '• Aprovação de cronograma e budget\\n'\n    content += '• **Prazo:** 2-3 dias úteis\\n\\n'\n\n    content += '**2. Aprovação de Conceitos**\\n'\n    content += '• Apresentação de direcionamento criativo\\n'\n    content += '• Validação de tom de voz e messaging\\n'\n    content += '• Aprovação de abordagem estratégica\\n'\n    content += '• **Prazo:** 3-5 dias úteis\\n\\n'\n\n    content += '**3. Aprovação de Materiais**\\n'\n    content += '• Revisão de peças criativas\\n'\n    content += '• Validação de textos e copy\\n'\n    content += '• Aprovação final para produção\\n'\n    content += '• **Prazo:** 2-3 dias úteis\\n\\n'\n\n    if (analysis.urgency === 'high') {\n      content += '### ⚡ Processo Acelerado\\n\\n'\n      content += '• **Aprovações Simultâneas:** Conceito + materiais juntos\\n'\n      content += '• **Prazo Reduzido:** 24-48h por aprovação\\n'\n      content += '• **Comunicação Direta:** Calls ao invés de e-mails\\n'\n      content += '• **Aprovação Condicional:** Implementar com ajustes menores\\n\\n'\n    }\n\n    content += '### Responsabilidades\\n\\n'\n    content += '**Cliente:**\\n'\n    content += '• Feedback em até 48h (ou 24h se urgente)\\n'\n    content += '• Consolidação de comentários internos\\n'\n    content += '• Decisões finais dentro do prazo\\n\\n'\n\n    content += '**Agência:**\\n'\n    content += '• Apresentações claras e objetivas\\n'\n    content += '• Justificativas estratégicas\\n'\n    content += '• Implementação de ajustes solicitados\\n'\n    content += '• Comunicação proativa sobre prazos\\n'\n\n    return {\n      title: '✅ Processo de Aprovação',\n      content,\n      type: 'detailed-guide',\n      priority: 6\n    }\n  }\n\n  private generateRiskAssessment(context: TemplateContext): ResponseSection {\n    const { analysis } = context\n\n    let content = '### Riscos Identificados\\n\\n'\n\n    // Add risks from analysis\n    if (analysis.riskFactors && analysis.riskFactors.length > 0) {\n      analysis.riskFactors.forEach((risk, index) => {\n        const severityEmoji = {\n          'low': '🟡',\n          'medium': '🟠',\n          'high': '🔴',\n          'critical': '🚨'\n        }[risk.severity] || '⚠️'\n\n        content += `**${severityEmoji} ${risk.type.toUpperCase()} - ${risk.severity.toUpperCase()}**\\n`\n        content += `${risk.description}\\n`\n        content += `**Probabilidade:** ${Math.round(risk.probability * 100)}% | **Impacto:** ${Math.round(risk.impact * 100)}%\\n`\n        content += '**Mitigação:**\\n'\n        risk.mitigation.forEach(action => {\n          content += `• ${action}\\n`\n        })\n        content += '\\n'\n      })\n    } else {\n      // Generate common risks based on analysis\n      if (analysis.urgency === 'high') {\n        content += '**🔴 CRONOGRAMA - ALTO**\\n'\n        content += 'Prazo apertado pode comprometer qualidade ou gerar estresse na equipe\\n'\n        content += '**Mitigação:**\\n'\n        content += '• Alocar recursos adicionais\\n'\n        content += '• Simplificar escopo se necessário\\n'\n        content += '• Comunicação intensiva com cliente\\n\\n'\n      }\n\n      if (analysis.completeness < 0.6) {\n        content += '**🟠 BRIEFING - MÉDIO**\\n'\n        content += 'Informações incompletas podem gerar retrabalho e atrasos\\n'\n        content += '**Mitigação:**\\n'\n        content += '• Reunião de alinhamento urgente\\n'\n        content += '• Checklist de informações obrigatórias\\n'\n        content += '• Validação constante com cliente\\n\\n'\n      }\n\n      if (analysis.complexity === 'advanced') {\n        content += '**🟠 COMPLEXIDADE - MÉDIO**\\n'\n        content += 'Projeto complexo requer expertise específica e coordenação cuidadosa\\n'\n        content += '**Mitigação:**\\n'\n        content += '• Envolver especialistas desde o início\\n'\n        content += '• Dividir em fases menores\\n'\n        content += '• Documentação detalhada de processos\\n\\n'\n      }\n    }\n\n    content += '### Monitoramento de Riscos\\n\\n'\n    content += '• **Revisões Semanais:** Avaliação de status dos riscos\\n'\n    content += '• **Alertas Antecipados:** Comunicação imediata se risco se materializar\\n'\n    content += '• **Planos de Contingência:** Ações alternativas preparadas\\n'\n    content += '• **Escalação:** Processo claro para decisões críticas\\n'\n\n    return {\n      title: '⚠️ Análise de Riscos',\n      content,\n      type: 'warning',\n      priority: 7\n    }\n  }\n\n  private generateBestPractices(context: TemplateContext): ResponseSection {\n    const { analysis, agentId } = context\n    const knowledge = this.knowledgeBase.getAgentKnowledge(agentId)\n\n    let content = '### Práticas Recomendadas\\n\\n'\n\n    // Add best practices from knowledge base\n    if (knowledge && knowledge.bestPractices.length > 0) {\n      const relevantPractices = knowledge.bestPractices.slice(0, 2) // Top 2 most relevant\n\n      relevantPractices.forEach(practice => {\n        content += `**${practice.title}**\\n`\n        content += `${practice.description}\\n\\n`\n        content += '**Implementação:**\\n'\n        practice.implementation.forEach(step => {\n          content += `• ${step}\\n`\n        })\n        content += '\\n**Benefícios:**\\n'\n        practice.benefits.forEach(benefit => {\n          content += `• ${benefit}\\n`\n        })\n        content += '\\n**Evitar:**\\n'\n        practice.commonMistakes.forEach(mistake => {\n          content += `• ${mistake}\\n`\n        })\n        content += '\\n'\n      })\n    }\n\n    // Add general best practices based on context\n    if (analysis.context.budget) {\n      content += '**Gestão de Budget**\\n'\n      content += '• Monitore gastos semanalmente\\n'\n      content += '• Reserve 10-15% para otimizações\\n'\n      content += '• Documente todas as decisões de investimento\\n'\n      content += '• Meça ROI de cada canal separadamente\\n\\n'\n    }\n\n    if (analysis.context.channels && analysis.context.channels.length > 1) {\n      content += '**Integração Multi-canal**\\n'\n      content += '• Mantenha mensagem consistente entre canais\\n'\n      content += '• Configure attribution modeling adequado\\n'\n      content += '• Teste criativos específicos para cada canal\\n'\n      content += '• Monitore performance comparativa\\n\\n'\n    }\n\n    content += '**Comunicação com Cliente**\\n'\n    content += '• Updates regulares mesmo sem novidades\\n'\n    content += '• Dados sempre acompanhados de insights\\n'\n    content += '• Transparência total sobre desafios\\n'\n    content += '• Recomendações proativas de melhorias\\n'\n\n    return {\n      title: '🎯 Melhores Práticas',\n      content,\n      type: 'best-practices',\n      priority: 5\n    }\n  }\n\n  private generateCommunicationAnalysis(context: TemplateContext): ResponseSection {\n    return { title: '📞 Análise da Situação', content: 'Análise da comunicação...', type: 'analysis', priority: 10 }\n  }\n\n  private generateCommunicationStrategy(context: TemplateContext): ResponseSection {\n    return { title: '💬 Estratégia de Comunicação', content: 'Estratégia de comunicação...', type: 'recommendation', priority: 9 }\n  }\n\n  private generateMeetingAgenda(context: TemplateContext): ResponseSection {\n    return { title: '📋 Agenda de Reunião Sugerida', content: 'Agenda de reunião...', type: 'detailed-guide', priority: 7 }\n  }\n\n  private generateStrategicOverview(context: TemplateContext): ResponseSection {\n    return { title: '🎯 Visão Estratégica', content: 'Visão estratégica...', type: 'analysis', priority: 10 }\n  }\n\n  private generateMarketAnalysis(context: TemplateContext): ResponseSection {\n    return { title: '📊 Análise de Mercado', content: 'Análise de mercado...', type: 'analysis', priority: 9 }\n  }\n\n  private generateCampaignPhases(context: TemplateContext): ResponseSection {\n    return { title: '📅 Fases da Campanha', content: 'Fases da campanha...', type: 'detailed-guide', priority: 8 }\n  }\n\n  private generateBudgetAllocation(context: TemplateContext): ResponseSection {\n    return { title: '💰 Distribuição de Budget', content: 'Distribuição de budget...', type: 'detailed-guide', priority: 8 }\n  }\n\n  private generateKPIFramework(context: TemplateContext): ResponseSection {\n    return { title: '📈 Framework de KPIs', content: 'Framework de KPIs...', type: 'detailed-guide', priority: 7 }\n  }\n\n  private generatePerformanceOverview(context: TemplateContext): ResponseSection {\n    return { title: '📊 Visão Geral da Performance', content: 'Visão geral da performance...', type: 'analysis', priority: 10 }\n  }\n\n  private generateDetailedMetrics(context: TemplateContext): ResponseSection {\n    return { title: '📈 Métricas Detalhadas', content: 'Métricas detalhadas...', type: 'analysis', priority: 9 }\n  }\n\n  private generateOptimizationOpportunities(context: TemplateContext): ResponseSection {\n    return { title: '🎯 Oportunidades de Otimização', content: 'Oportunidades de otimização...', type: 'recommendation', priority: 8 }\n  }\n\n  private generateCompetitiveInsights(context: TemplateContext): ResponseSection {\n    return { title: '🏆 Insights Competitivos', content: 'Insights competitivos...', type: 'analysis', priority: 7 }\n  }\n\n  private generateFallbackResponse(context: TemplateContext): ResponseSection[] {\n    return [{\n      title: '📋 Análise Geral',\n      content: 'Análise geral do input fornecido...',\n      type: 'analysis',\n      priority: 5\n    }]\n  }\n\n  // Utility methods\n  private translateComplexity(complexity: string): string {\n    const translations = {\n      'basic': 'Básica',\n      'intermediate': 'Intermediária',\n      'advanced': 'Avançada'\n    }\n    return translations[complexity as keyof typeof translations] || complexity\n  }\n\n  private translateUrgency(urgency: string): string {\n    const translations = {\n      'low': 'Baixa',\n      'medium': 'Média',\n      'high': 'Alta'\n    }\n    return translations[urgency as keyof typeof translations] || urgency\n  }\n\n  private translateIntent(intent: string): string {\n    const translations = {\n      'create_briefing': 'Criação de Briefing',\n      'analyze_performance': 'Análise de Performance',\n      'plan_campaign': 'Planejamento de Campanha',\n      'optimize': 'Otimização',\n      'report': 'Relatório',\n      'troubleshoot': 'Resolução de Problemas',\n      'general_inquiry': 'Consulta Geral'\n    }\n    return translations[intent as keyof typeof translations] || intent\n  }\n\n  private translateSentiment(sentiment: string): string {\n    const translations = {\n      'positive': 'positivo',\n      'neutral': 'neutro',\n      'negative': 'negativo'\n    }\n    return translations[sentiment as keyof typeof translations] || sentiment\n  }\n\n  private formatContextKey(key: string): string {\n    const translations = {\n      'budget': 'Orçamento',\n      'timeline': 'Cronograma',\n      'targetAge': 'Faixa Etária',\n      'channels': 'Canais'\n    }\n    return translations[key as keyof typeof translations] || key\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;;AAGD;;;AA4BO,MAAM;IAUX,OAAc,cAAsC;QAClD,IAAI,CAAC,uBAAuB,QAAQ,EAAE;YACpC,uBAAuB,QAAQ,GAAG,IAAI;QACxC;QACA,OAAO,uBAAuB,QAAQ;IACxC;IAEO,iBAAiB,OAAwB,EAAqB;QACnE,MAAM,iBAAiB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,OAAO,KAAK,EAAE;QAEhE,4BAA4B;QAC5B,MAAM,sBAAsB,eACzB,MAAM,CAAC,CAAA,WAAY,SAAS,cAAc,CAAC,UAC3C,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ;QAEzC,+CAA+C;QAC/C,MAAM,mBAAmB,mBAAmB,CAAC,EAAE;QAC/C,IAAI,CAAC,kBAAkB;YACrB,OAAO,IAAI,CAAC,wBAAwB,CAAC;QACvC;QAEA,kCAAkC;QAClC,MAAM,WAA8B,EAAE;QAEtC,KAAK,MAAM,cAAc,iBAAiB,QAAQ,CAAE;YAClD,6CAA6C;YAC7C,IAAI,WAAW,SAAS,IAAI,CAAC,WAAW,SAAS,CAAC,UAAU;gBAC1D;YACF;YAEA,IAAI;gBACF,MAAM,UAAU,WAAW,SAAS,CAAC;gBACrC,SAAS,IAAI,CAAC;YAChB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,AAAC,4BAAyC,OAAd,WAAW,EAAE,EAAC,MAAI;YAC5D,+BAA+B;YACjC;QACF;QAEA,4BAA4B;QAC5B,OAAO,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ;IACxD;IAEQ,sBAA4B;QAClC,8BAA8B;QAC9B,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,eAAe;YAChC;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,gBAAgB,CAAC,UACf,QAAQ,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,eACjC,QAAQ,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,eACnC,QAAQ,QAAQ,CAAC,MAAM,KAAK;gBAC9B,UAAU;gBACV,UAAU;oBACR;wBACE,IAAI;wBACJ,OAAO;wBACP,MAAM;wBACN,UAAU;wBACV,UAAU;wBACV,WAAW,CAAC,UAAY,IAAI,CAAC,wBAAwB,CAAC;oBACxD;oBACA;wBACE,IAAI;wBACJ,OAAO;wBACP,MAAM;wBACN,UAAU;wBACV,UAAU;wBACV,WAAW,CAAC,UAAY,IAAI,CAAC,wBAAwB,CAAC;oBACxD;oBACA;wBACE,IAAI;wBACJ,OAAO;wBACP,MAAM;wBACN,UAAU;wBACV,UAAU;wBACV,WAAW,CAAC,UAAY,QAAQ,QAAQ,CAAC,YAAY,GAAG;wBACxD,WAAW,CAAC,UAAY,IAAI,CAAC,0BAA0B,CAAC;oBAC1D;oBACA;wBACE,IAAI;wBACJ,OAAO;wBACP,MAAM;wBACN,UAAU;wBACV,UAAU;wBACV,WAAW,CAAC,UAAY,IAAI,CAAC,gCAAgC,CAAC;oBAChE;oBACA;wBACE,IAAI;wBACJ,OAAO;wBACP,MAAM;wBACN,UAAU;wBACV,UAAU;wBACV,WAAW,CAAC,UAAY,IAAI,CAAC,yBAAyB,CAAC;oBACzD;oBACA;wBACE,IAAI;wBACJ,OAAO;wBACP,MAAM;wBACN,UAAU;wBACV,UAAU;wBACV,WAAW,CAAC,UAAY,IAAI,CAAC,uBAAuB,CAAC;oBACvD;oBACA;wBACE,IAAI;wBACJ,OAAO;wBACP,MAAM;wBACN,UAAU;wBACV,UAAU;wBACV,WAAW,CAAC,UAAY,QAAQ,QAAQ,CAAC,OAAO,KAAK,UAAU,QAAQ,QAAQ,CAAC,UAAU,KAAK;wBAC/F,WAAW,CAAC,UAAY,IAAI,CAAC,sBAAsB,CAAC;oBACtD;oBACA;wBACE,IAAI;wBACJ,OAAO;wBACP,MAAM;wBACN,UAAU;wBACV,UAAU;wBACV,WAAW,CAAC,UAAY,IAAI,CAAC,qBAAqB,CAAC;oBACrD;oBACA;wBACE,IAAI;wBACJ,OAAO;wBACP,MAAM;wBACN,UAAU;wBACV,UAAU;wBACV,WAAW,CAAC,UAAY,IAAI,CAAC,iBAAiB,CAAC;oBACjD;iBACD;YACH;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,gBAAgB,CAAC,UACf,QAAQ,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA,IAAK;4BAAC;4BAAW;4BAAe;4BAAe;yBAAU,CAAC,QAAQ,CAAC;gBACpG,UAAU;gBACV,UAAU;oBACR;wBACE,IAAI;wBACJ,OAAO;wBACP,MAAM;wBACN,UAAU;wBACV,UAAU;wBACV,WAAW,CAAC,UAAY,IAAI,CAAC,6BAA6B,CAAC;oBAC7D;oBACA;wBACE,IAAI;wBACJ,OAAO;wBACP,MAAM;wBACN,UAAU;wBACV,UAAU;wBACV,WAAW,CAAC,UAAY,IAAI,CAAC,6BAA6B,CAAC;oBAC7D;oBACA;wBACE,IAAI;wBACJ,OAAO;wBACP,MAAM;wBACN,UAAU;wBACV,UAAU;wBACV,WAAW,CAAC,UAAY,QAAQ,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC;wBAC3D,WAAW,CAAC,UAAY,IAAI,CAAC,qBAAqB,CAAC;oBACrD;iBACD;YACH;SACD;QAED,+BAA+B;QAC/B,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,gBAAgB;YACjC;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,gBAAgB,CAAC,UACf,QAAQ,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,WACjC,QAAQ,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,mBACnC,QAAQ,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC;gBACrC,UAAU;gBACV,UAAU;oBACR;wBACE,IAAI;wBACJ,OAAO;wBACP,MAAM;wBACN,UAAU;wBACV,UAAU;wBACV,WAAW,CAAC,UAAY,IAAI,CAAC,yBAAyB,CAAC;oBACzD;oBACA;wBACE,IAAI;wBACJ,OAAO;wBACP,MAAM;wBACN,UAAU;wBACV,UAAU;wBACV,WAAW,CAAC,UAAY,IAAI,CAAC,sBAAsB,CAAC;oBACtD;oBACA;wBACE,IAAI;wBACJ,OAAO;wBACP,MAAM;wBACN,UAAU;wBACV,UAAU;wBACV,WAAW,CAAC,UAAY,IAAI,CAAC,sBAAsB,CAAC;oBACtD;oBACA;wBACE,IAAI;wBACJ,OAAO;wBACP,MAAM;wBACN,UAAU;wBACV,UAAU;wBACV,WAAW,CAAC,UAAY,IAAI,CAAC,wBAAwB,CAAC;oBACxD;oBACA;wBACE,IAAI;wBACJ,OAAO;wBACP,MAAM;wBACN,UAAU;wBACV,UAAU;wBACV,WAAW,CAAC,UAAY,IAAI,CAAC,oBAAoB,CAAC;oBACpD;iBACD;YACH;SACD;QAED,wBAAwB;QACxB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS;YAC1B;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,gBAAgB,CAAC,UACf,QAAQ,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,cACjC,QAAQ,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA,IAAK;4BAAC;4BAAe;4BAAa;4BAAW;yBAAW,CAAC,QAAQ,CAAC;gBACnG,UAAU;gBACV,UAAU;oBACR;wBACE,IAAI;wBACJ,OAAO;wBACP,MAAM;wBACN,UAAU;wBACV,UAAU;wBACV,WAAW,CAAC,UAAY,IAAI,CAAC,2BAA2B,CAAC;oBAC3D;oBACA;wBACE,IAAI;wBACJ,OAAO;wBACP,MAAM;wBACN,UAAU;wBACV,UAAU;wBACV,WAAW,CAAC,UAAY,IAAI,CAAC,uBAAuB,CAAC;oBACvD;oBACA;wBACE,IAAI;wBACJ,OAAO;wBACP,MAAM;wBACN,UAAU;wBACV,UAAU;wBACV,WAAW,CAAC,UAAY,IAAI,CAAC,iCAAiC,CAAC;oBACjE;oBACA;wBACE,IAAI;wBACJ,OAAO;wBACP,MAAM;wBACN,UAAU;wBACV,UAAU;wBACV,WAAW,CAAC,UAAY,IAAI,CAAC,2BAA2B,CAAC;oBAC3D;iBACD;YACH;SACD;IACH;IAEA,oCAAoC;IAC5B,yBAAyB,OAAwB,EAAmB;QAC1E,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG;QAE5B,IAAI,UAAU,AAAC,sCAA6E,OAAxC,KAAK,KAAK,CAAC,SAAS,YAAY,GAAG,MAAK;QAC5F,WAAW,AAAC,qBAAkE,OAA9C,IAAI,CAAC,mBAAmB,CAAC,SAAS,UAAU,GAAE;QAC9E,WAAW,AAAC,iBAAwD,OAAxC,IAAI,CAAC,gBAAgB,CAAC,SAAS,OAAO,GAAE;QACpE,WAAW,AAAC,2BAAgE,OAAtC,IAAI,CAAC,eAAe,CAAC,SAAS,MAAM,GAAE;QAE5E,IAAI,SAAS,OAAO,CAAC,MAAM,EAAE;YAC3B,WAAW,AAAC,+BAAsD,OAAxB,SAAS,OAAO,CAAC,MAAM,EAAC;QACpE;QACA,IAAI,SAAS,OAAO,CAAC,QAAQ,EAAE;YAC7B,WAAW,AAAC,cAAuC,OAA1B,SAAS,OAAO,CAAC,QAAQ,EAAC;QACrD;QACA,IAAI,SAAS,OAAO,CAAC,QAAQ,IAAI,SAAS,OAAO,CAAC,QAAQ,CAAC,MAAM,GAAG,GAAG;YACrE,WAAW,AAAC,2BAA+D,OAArC,SAAS,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAM;QAC7E;QAEA,OAAO;YACL,OAAO;YACP;YACA,MAAM;YACN,UAAU;YACV,UAAU;gBAAE,YAAY,SAAS,YAAY;YAAC;QAChD;IACF;IAEQ,yBAAyB,OAAwB,EAAmB;QAC1E,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG;QAE5B,IAAI,UAAU;QAEd,IAAI,SAAS,QAAQ,CAAC,MAAM,GAAG,GAAG;YAChC,WAAW,AAAC,kCAA0E,OAAzC,SAAS,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,OAAM;QACxF;QAEA,IAAI,SAAS,QAAQ,CAAC,MAAM,GAAG,GAAG;YAChC,WAAW,AAAC,6BAAyD,OAA7B,SAAS,QAAQ,CAAC,IAAI,CAAC,OAAM;QACvE;QAEA,WAAW;QAEX,IAAI,OAAO,IAAI,CAAC,SAAS,OAAO,EAAE,MAAM,GAAG,GAAG;YAC5C,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,SAAS,OAAO,EAAG;gBAC3D,WAAW,AAAC,OAAuC,OAAjC,IAAI,CAAC,gBAAgB,CAAC,MAAK,QAAY,OAAN,OAAM;YAC3D;QACF,OAAO;YACL,WAAW;QACb;QAEA,WAAW;QACX,WAAW,AAAC,kCAA6E,OAA5C,IAAI,CAAC,kBAAkB,CAAC,SAAS,SAAS,GAAE;QAEzF,OAAQ,SAAS,SAAS;YACxB,KAAK;gBACH,WAAW;gBACX;YACF,KAAK;gBACH,WAAW;gBACX;YACF;gBACE,WAAW;QACf;QAEA,OAAO;YACL,OAAO;YACP;YACA,MAAM;YACN,UAAU;QACZ;IACF;IAEQ,2BAA2B,OAAwB,EAAmB;QAC5E,MAAM,EAAE,QAAQ,EAAE,GAAG;QACrB,MAAM,UAAoB,EAAE;QAE5B,IAAI,CAAC,SAAS,OAAO,CAAC,MAAM,EAAE,QAAQ,IAAI,CAAC;QAC3C,IAAI,CAAC,SAAS,OAAO,CAAC,QAAQ,EAAE,QAAQ,IAAI,CAAC;QAC7C,IAAI,CAAC,SAAS,OAAO,CAAC,SAAS,EAAE,QAAQ,IAAI,CAAC;QAC9C,IAAI,CAAC,SAAS,OAAO,CAAC,QAAQ,EAAE,QAAQ,IAAI,CAAC;QAE7C,IAAI,UAAU;QAEd,IAAI,QAAQ,MAAM,GAAG,GAAG;YACtB,QAAQ,OAAO,CAAC,CAAA;gBACd,WAAW,AAAC,KAAS,OAAL,MAAK;YACvB;YAEA,WAAW;YACX,WAAW;YACX,WAAW;YACX,WAAW;YACX,WAAW;YAEX,WAAW;YACX,WAAW;QACb,OAAO;YACL,WAAW;QACb;QAEA,OAAO;YACL,OAAO;YACP;YACA,MAAM;YACN,UAAU;QACZ;IACF;IAEQ,iCAAiC,OAAwB,EAAmB;QAClF,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG;QAC5B,MAAM,YAAY,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,QAAQ,OAAO;QACtE,MAAM,WAAW,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,QAAQ,OAAO,EAAE;QAEjE,IAAI,UAAU;QAEd,mCAAmC;QACnC,SAAS,OAAO,CAAC,CAAC,SAAS;YACzB,WAAW,AAAC,GAAkB,OAAhB,QAAQ,GAAE,QAA2C,OAArC,QAAQ,QAAQ,CAAC,WAAW,IAAG,QAAsB,OAAhB,QAAQ,OAAO,EAAC;QACrF;QAEA,iDAAiD;QACjD,IAAI,SAAS,OAAO,KAAK,QAAQ;YAC/B,WAAW;YACX,WAAW;YACX,WAAW;QACb;QAEA,IAAI,SAAS,UAAU,KAAK,YAAY;YACtC,WAAW;YACX,WAAW;YACX,WAAW;QACb;QAEA,+BAA+B;QAC/B,IAAI,SAAS,OAAO,CAAC,MAAM,EAAE;YAC3B,MAAM,cAAc,WAAW,SAAS,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU;YACzE,IAAI,cAAc,OAAO;gBACvB,WAAW;gBACX,WAAW;YACb,OAAO,IAAI,cAAc,QAAQ;gBAC/B,WAAW;gBACX,WAAW;YACb;QACF;QAEA,mCAAmC;QACnC,IAAI,SAAS,OAAO,CAAC,QAAQ,EAAE;YAC7B,WAAW,oEAAoE,SAAS,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ;YACtH,WAAW;QACb;QAEA,kDAAkD;QAClD,IAAI,SAAS,MAAM,KAAK,KAAK,SAAS,OAAO,KAAK,SAAS,SAAS,UAAU,KAAK,SAAS;YAC1F,WAAW;YACX,WAAW;YACX,WAAW;YACX,WAAW;QACb;QAEA,OAAO;YACL,OAAO;YACP;YACA,MAAM;YACN,UAAU;QACZ;IACF;IAEQ,kBAAkB,OAAwB,EAAmB;QACnE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG;QAErC,IAAI,UAAU;QAEd,mCAAmC;QACnC,IAAI,YAAY,eAAe;YAC7B,WAAW;YACX,IAAI,SAAS,YAAY,GAAG,KAAK;gBAC/B,WAAW;YACb,OAAO;gBACL,WAAW;YACb;YACA,WAAW;YACX,WAAW;QACb,OAAO,IAAI,YAAY,gBAAgB;YACrC,WAAW;YACX,WAAW;YACX,WAAW;YACX,WAAW;QACb,OAAO,IAAI,YAAY,SAAS;YAC9B,WAAW;YACX,WAAW;YACX,WAAW;YACX,WAAW;QACb;QAEA,IAAI,SAAS,OAAO,KAAK,QAAQ;YAC/B,WAAW;QACb;QAEA,WAAW;QAEX,4CAA4C;QAC5C,IAAI,SAAS,OAAO,CAAC,MAAM,EAAE;YAC3B,MAAM,cAAc,WAAW,SAAS,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU;YACzE,IAAI,cAAc,OAAO;gBACvB,WAAW;gBACX,WAAW;YACb,OAAO;gBACL,WAAW;gBACX,WAAW;YACb;QACF,OAAO;YACL,WAAW;QACb;QAEA,WAAW;QACX,WAAW;QAEX,IAAI,YAAY,eAAe;YAC7B,WAAW;YACX,WAAW;QACb,OAAO,IAAI,YAAY,gBAAgB;YACrC,WAAW;YACX,WAAW;QACb,OAAO,IAAI,YAAY,SAAS;YAC9B,WAAW;YACX,WAAW;QACb;QAEA,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QAEX,WAAW;QACX,IAAI,SAAS,OAAO,KAAK,QAAQ;YAC/B,WAAW;YACX,WAAW;QACb,OAAO;YACL,WAAW;YACX,WAAW;QACb;QACA,WAAW;QACX,WAAW;QAEX,OAAO;YACL,OAAO;YACP;YACA,MAAM;YACN,UAAU;QACZ;IACF;IAEQ,0BAA0B,OAAwB,EAAmB;QAC3E,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG;QAE9B,IAAI,UAAU;QAEd,sCAAsC;QACtC,IAAI,aAAa,EAAE,UAAU;;QAC7B,IAAI,SAAS,OAAO,CAAC,QAAQ,EAAE;YAC7B,MAAM,eAAe,SAAS,OAAO,CAAC,QAAQ,CAAC,WAAW;YAC1D,IAAI,aAAa,QAAQ,CAAC,QAAQ;oBACV;gBAAtB,MAAM,OAAO,SAAS,EAAA,sBAAA,aAAa,KAAK,CAAC,oBAAnB,0CAAA,mBAA2B,CAAC,EAAE,KAAI;gBACxD,aAAa,KAAK,IAAI,CAAC,OAAO;YAChC,OAAO,IAAI,aAAa,QAAQ,CAAC,WAAW;oBACpB;gBAAtB,aAAa,SAAS,EAAA,uBAAA,aAAa,KAAK,CAAC,oBAAnB,2CAAA,oBAA2B,CAAC,EAAE,KAAI;YAC1D,OAAO,IAAI,aAAa,QAAQ,CAAC,QAAQ;oBACf;gBAAxB,MAAM,SAAS,SAAS,EAAA,uBAAA,aAAa,KAAK,CAAC,oBAAnB,2CAAA,oBAA2B,CAAC,EAAE,KAAI;gBAC1D,aAAa,SAAS;YACxB;QACF;QAEA,IAAI,SAAS,OAAO,KAAK,QAAQ;YAC/B,aAAa,KAAK,GAAG,CAAC,GAAG,KAAK,IAAI,CAAC,aAAa,OAAM,oCAAoC;QAC5F;QAEA,gCAAgC;QAChC,IAAI,cAAc,GAAG;YACnB,WAAW;YACX,WAAW;YACX,WAAW;YACX,WAAW;YACX,WAAW;YACX,WAAW;YACX,WAAW;YACX,WAAW;YACX,WAAW;QACb,OAAO,IAAI,cAAc,GAAG;YAC1B,WAAW;YACX,WAAW;YACX,WAAW;YACX,WAAW;YACX,WAAW;YACX,WAAW;YACX,WAAW;YACX,WAAW;YACX,WAAW;YACX,WAAW;YACX,WAAW;YACX,WAAW;YACX,WAAW;YACX,WAAW;YACX,WAAW;YACX,WAAW;YACX,WAAW;YACX,WAAW;YACX,WAAW;YACX,WAAW;YACX,WAAW;QACb,OAAO;YACL,WAAW,AAAC,8BAAwC,OAAX,YAAW;YACpD,MAAM,gBAAgB,KAAK,IAAI,CAAC,aAAa;YAC7C,WAAW,AAAC,4BAAyC,OAAd,eAAc;YACrD,WAAW;YACX,WAAW;YACX,WAAW;YACX,WAAW;YACX,WAAW,AAAC,+BAA4C,OAAd,eAAc;YACxD,WAAW;YACX,WAAW;YACX,WAAW;YACX,WAAW;YACX,WAAW,AAAC,0BAAuC,OAAd,eAAc;YACnD,WAAW;YACX,WAAW;YACX,WAAW;YACX,WAAW;YACX,WAAW,AAAC,4BAA4D,OAAjC,aAAc,gBAAgB,GAAG;YACxE,WAAW;YACX,WAAW;YACX,WAAW;YACX,WAAW;QACb;QAEA,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QAEX,OAAO;YACL,OAAO;YACP;YACA,MAAM;YACN,UAAU;QACZ;IACF;IAEQ,wBAAwB,OAAwB,EAAmB;QACzE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG;QAE9B,IAAI,UAAU;QAEd,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QAEX,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QAEX,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QAEX,IAAI,SAAS,OAAO,KAAK,QAAQ;YAC/B,WAAW;YACX,WAAW;YACX,WAAW;YACX,WAAW;YACX,WAAW;QACb;QAEA,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QAEX,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QAEX,OAAO;YACL,OAAO;YACP;YACA,MAAM;YACN,UAAU;QACZ;IACF;IAEQ,uBAAuB,OAAwB,EAAmB;QACxE,MAAM,EAAE,QAAQ,EAAE,GAAG;QAErB,IAAI,UAAU;QAEd,0BAA0B;QAC1B,IAAI,SAAS,WAAW,IAAI,SAAS,WAAW,CAAC,MAAM,GAAG,GAAG;YAC3D,SAAS,WAAW,CAAC,OAAO,CAAC,CAAC,MAAM;gBAClC,MAAM,gBAAgB;oBACpB,OAAO;oBACP,UAAU;oBACV,QAAQ;oBACR,YAAY;gBACd,CAAC,CAAC,KAAK,QAAQ,CAAC,IAAI;gBAEpB,WAAW,AAAC,KAAqB,OAAjB,eAAc,KAAgC,OAA7B,KAAK,IAAI,CAAC,WAAW,IAAG,OAAiC,OAA5B,KAAK,QAAQ,CAAC,WAAW,IAAG;gBAC1F,WAAW,AAAC,GAAmB,OAAjB,KAAK,WAAW,EAAC;gBAC/B,WAAW,AAAC,sBAA2E,OAAtD,KAAK,KAAK,CAAC,KAAK,WAAW,GAAG,MAAK,qBAAiD,OAA9B,KAAK,KAAK,CAAC,KAAK,MAAM,GAAG,MAAK;gBACrH,WAAW;gBACX,KAAK,UAAU,CAAC,OAAO,CAAC,CAAA;oBACtB,WAAW,AAAC,KAAW,OAAP,QAAO;gBACzB;gBACA,WAAW;YACb;QACF,OAAO;YACL,0CAA0C;YAC1C,IAAI,SAAS,OAAO,KAAK,QAAQ;gBAC/B,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,WAAW;YACb;YAEA,IAAI,SAAS,YAAY,GAAG,KAAK;gBAC/B,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,WAAW;YACb;YAEA,IAAI,SAAS,UAAU,KAAK,YAAY;gBACtC,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,WAAW;YACb;QACF;QAEA,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QAEX,OAAO;YACL,OAAO;YACP;YACA,MAAM;YACN,UAAU;QACZ;IACF;IAEQ,sBAAsB,OAAwB,EAAmB;QACvE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG;QAC9B,MAAM,YAAY,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC;QAEvD,IAAI,UAAU;QAEd,yCAAyC;QACzC,IAAI,aAAa,UAAU,aAAa,CAAC,MAAM,GAAG,GAAG;YACnD,MAAM,oBAAoB,UAAU,aAAa,CAAC,KAAK,CAAC,GAAG,GAAG,sBAAsB;;YAEpF,kBAAkB,OAAO,CAAC,CAAA;gBACxB,WAAW,AAAC,KAAmB,OAAf,SAAS,KAAK,EAAC;gBAC/B,WAAW,AAAC,GAAuB,OAArB,SAAS,WAAW,EAAC;gBACnC,WAAW;gBACX,SAAS,cAAc,CAAC,OAAO,CAAC,CAAA;oBAC9B,WAAW,AAAC,KAAS,OAAL,MAAK;gBACvB;gBACA,WAAW;gBACX,SAAS,QAAQ,CAAC,OAAO,CAAC,CAAA;oBACxB,WAAW,AAAC,KAAY,OAAR,SAAQ;gBAC1B;gBACA,WAAW;gBACX,SAAS,cAAc,CAAC,OAAO,CAAC,CAAA;oBAC9B,WAAW,AAAC,KAAY,OAAR,SAAQ;gBAC1B;gBACA,WAAW;YACb;QACF;QAEA,8CAA8C;QAC9C,IAAI,SAAS,OAAO,CAAC,MAAM,EAAE;YAC3B,WAAW;YACX,WAAW;YACX,WAAW;YACX,WAAW;YACX,WAAW;QACb;QAEA,IAAI,SAAS,OAAO,CAAC,QAAQ,IAAI,SAAS,OAAO,CAAC,QAAQ,CAAC,MAAM,GAAG,GAAG;YACrE,WAAW;YACX,WAAW;YACX,WAAW;YACX,WAAW;YACX,WAAW;QACb;QAEA,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QAEX,OAAO;YACL,OAAO;YACP;YACA,MAAM;YACN,UAAU;QACZ;IACF;IAEQ,8BAA8B,OAAwB,EAAmB;QAC/E,OAAO;YAAE,OAAO;YAA0B,SAAS;YAA6B,MAAM;YAAY,UAAU;QAAG;IACjH;IAEQ,8BAA8B,OAAwB,EAAmB;QAC/E,OAAO;YAAE,OAAO;YAAgC,SAAS;YAAgC,MAAM;YAAkB,UAAU;QAAE;IAC/H;IAEQ,sBAAsB,OAAwB,EAAmB;QACvE,OAAO;YAAE,OAAO;YAAiC,SAAS;YAAwB,MAAM;YAAkB,UAAU;QAAE;IACxH;IAEQ,0BAA0B,OAAwB,EAAmB;QAC3E,OAAO;YAAE,OAAO;YAAwB,SAAS;YAAwB,MAAM;YAAY,UAAU;QAAG;IAC1G;IAEQ,uBAAuB,OAAwB,EAAmB;QACxE,OAAO;YAAE,OAAO;YAAyB,SAAS;YAAyB,MAAM;YAAY,UAAU;QAAE;IAC3G;IAEQ,uBAAuB,OAAwB,EAAmB;QACxE,OAAO;YAAE,OAAO;YAAwB,SAAS;YAAwB,MAAM;YAAkB,UAAU;QAAE;IAC/G;IAEQ,yBAAyB,OAAwB,EAAmB;QAC1E,OAAO;YAAE,OAAO;YAA6B,SAAS;YAA6B,MAAM;YAAkB,UAAU;QAAE;IACzH;IAEQ,qBAAqB,OAAwB,EAAmB;QACtE,OAAO;YAAE,OAAO;YAAwB,SAAS;YAAwB,MAAM;YAAkB,UAAU;QAAE;IAC/G;IAEQ,4BAA4B,OAAwB,EAAmB;QAC7E,OAAO;YAAE,OAAO;YAAiC,SAAS;YAAiC,MAAM;YAAY,UAAU;QAAG;IAC5H;IAEQ,wBAAwB,OAAwB,EAAmB;QACzE,OAAO;YAAE,OAAO;YAA0B,SAAS;YAA0B,MAAM;YAAY,UAAU;QAAE;IAC7G;IAEQ,kCAAkC,OAAwB,EAAmB;QACnF,OAAO;YAAE,OAAO;YAAkC,SAAS;YAAkC,MAAM;YAAkB,UAAU;QAAE;IACnI;IAEQ,4BAA4B,OAAwB,EAAmB;QAC7E,OAAO;YAAE,OAAO;YAA4B,SAAS;YAA4B,MAAM;YAAY,UAAU;QAAE;IACjH;IAEQ,yBAAyB,OAAwB,EAAqB;QAC5E,OAAO;YAAC;gBACN,OAAO;gBACP,SAAS;gBACT,MAAM;gBACN,UAAU;YACZ;SAAE;IACJ;IAEA,kBAAkB;IACV,oBAAoB,UAAkB,EAAU;QACtD,MAAM,eAAe;YACnB,SAAS;YACT,gBAAgB;YAChB,YAAY;QACd;QACA,OAAO,YAAY,CAAC,WAAwC,IAAI;IAClE;IAEQ,iBAAiB,OAAe,EAAU;QAChD,MAAM,eAAe;YACnB,OAAO;YACP,UAAU;YACV,QAAQ;QACV;QACA,OAAO,YAAY,CAAC,QAAqC,IAAI;IAC/D;IAEQ,gBAAgB,MAAc,EAAU;QAC9C,MAAM,eAAe;YACnB,mBAAmB;YACnB,uBAAuB;YACvB,iBAAiB;YACjB,YAAY;YACZ,UAAU;YACV,gBAAgB;YAChB,mBAAmB;QACrB;QACA,OAAO,YAAY,CAAC,OAAoC,IAAI;IAC9D;IAEQ,mBAAmB,SAAiB,EAAU;QACpD,MAAM,eAAe;YACnB,YAAY;YACZ,WAAW;YACX,YAAY;QACd;QACA,OAAO,YAAY,CAAC,UAAuC,IAAI;IACjE;IAEQ,iBAAiB,GAAW,EAAU;QAC5C,MAAM,eAAe;YACnB,UAAU;YACV,YAAY;YACZ,aAAa;YACb,YAAY;QACd;QACA,OAAO,YAAY,CAAC,IAAiC,IAAI;IAC3D;IAj5BA,aAAsB;QAHtB,+KAAQ,aAA4C,IAAI;QACxD,+KAAQ,iBAAR,KAAA;QAGE,IAAI,CAAC,aAAa,GAAG,8JAAkB,CAAC,WAAW;QACnD,IAAI,CAAC,mBAAmB;IAC1B;AA+4BF;AAt5BE,yKADW,wBACI,YAAf,KAAA", "debugId": null}}, {"offset": {"line": 3051, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent3/agentepub/src/lib/agents/enhancedAgentSystem.ts"], "sourcesContent": ["/**\n * Enhanced Agent System\n * \n * This module integrates all components of the enhanced response system to provide\n * comprehensive, intelligent, and contextually relevant agent responses.\n */\n\nimport { ResponseEngine, EnhancedResponse, InputAnalysis } from './responseEngine'\nimport { IntelligenceLayer, AdvancedAnalysis } from './intelligenceLayer'\nimport { ResponseTemplateEngine, TemplateContext } from './responseTemplates'\nimport { AgentKnowledgeBase } from './knowledgeBase'\n\nexport interface AgentResponse {\n  id: string\n  agentId: string\n  input: string\n  analysis: AdvancedAnalysis\n  response: EnhancedResponse\n  formattedOutput: string\n  metadata: ResponseMetadata\n  timestamp: Date\n}\n\nexport interface ResponseMetadata {\n  processingTime: number\n  confidence: number\n  completeness: number\n  qualityScore: number\n  templateUsed: string\n  riskFactors: number\n  opportunities: number\n  followUpRecommended: boolean\n}\n\nexport interface AgentConfig {\n  id: string\n  name: string\n  specialization: string[]\n  responseStyle: 'concise' | 'detailed' | 'comprehensive'\n  minQualityThreshold: number\n  enableAdvancedAnalysis: boolean\n}\n\nexport class EnhancedAgentSystem {\n  private static instance: EnhancedAgentSystem\n  private responseEngine: ResponseEngine\n  private intelligenceLayer: IntelligenceLayer\n  private templateEngine: ResponseTemplateEngine\n  private knowledgeBase: AgentKnowledgeBase\n  private agentConfigs: Map<string, AgentConfig> = new Map()\n\n  private constructor() {\n    this.responseEngine = ResponseEngine.getInstance()\n    this.intelligenceLayer = IntelligenceLayer.getInstance()\n    this.templateEngine = ResponseTemplateEngine.getInstance()\n    this.knowledgeBase = AgentKnowledgeBase.getInstance()\n    this.initializeAgentConfigs()\n  }\n\n  public static getInstance(): EnhancedAgentSystem {\n    if (!EnhancedAgentSystem.instance) {\n      EnhancedAgentSystem.instance = new EnhancedAgentSystem()\n    }\n    return EnhancedAgentSystem.instance\n  }\n\n  /**\n   * Main method to generate enhanced agent responses\n   */\n  public async generateResponse(agentId: string, input: string, userContext?: Record<string, any>): Promise<AgentResponse> {\n    const startTime = Date.now()\n    const config = this.agentConfigs.get(agentId)\n    \n    if (!config) {\n      throw new Error(`Agent configuration not found for: ${agentId}`)\n    }\n\n    try {\n      // Step 1: Perform advanced input analysis\n      const analysis = config.enableAdvancedAnalysis \n        ? this.intelligenceLayer.performAdvancedAnalysis(input)\n        : this.performBasicAnalysis(input)\n\n      // Step 2: Generate template-based response\n      const templateContext: TemplateContext = {\n        agentId,\n        analysis,\n        input,\n        userContext\n      }\n      \n      const templateSections = this.templateEngine.generateResponse(templateContext)\n\n      // Step 3: Generate enhanced response using response engine\n      const enhancedResponse = this.responseEngine.generateResponse(agentId, input, analysis)\n      \n      // Step 4: Merge template sections with enhanced response\n      const mergedSections = this.mergeSections(templateSections, enhancedResponse.sections)\n      \n      // Step 5: Apply agent-specific enhancements\n      const finalResponse: EnhancedResponse = {\n        ...enhancedResponse,\n        sections: mergedSections,\n        confidence: this.calculateFinalConfidence(analysis, mergedSections),\n        completeness: this.calculateFinalCompleteness(analysis, mergedSections)\n      }\n\n      // Step 6: Format output based on agent style\n      const formattedOutput = this.formatResponse(finalResponse, config.responseStyle)\n\n      // Step 7: Calculate metadata\n      const processingTime = Date.now() - startTime\n      const metadata = this.calculateMetadata(analysis, finalResponse, processingTime, config)\n\n      // Step 8: Quality validation\n      if (metadata.qualityScore < config.minQualityThreshold) {\n        console.warn(`Response quality below threshold for agent ${agentId}: ${metadata.qualityScore}`)\n        // Could trigger fallback or enhancement here\n      }\n\n      return {\n        id: this.generateResponseId(),\n        agentId,\n        input,\n        analysis,\n        response: finalResponse,\n        formattedOutput,\n        metadata,\n        timestamp: new Date()\n      }\n\n    } catch (error) {\n      console.error(`Error generating response for agent ${agentId}:`, error)\n      throw new Error(`Failed to generate response: ${error instanceof Error ? error.message : 'Unknown error'}`)\n    }\n  }\n\n  /**\n   * Batch processing for multiple inputs\n   */\n  public async generateBatchResponses(\n    agentId: string, \n    inputs: string[], \n    userContext?: Record<string, any>\n  ): Promise<AgentResponse[]> {\n    const responses: AgentResponse[] = []\n    \n    for (const input of inputs) {\n      try {\n        const response = await this.generateResponse(agentId, input, userContext)\n        responses.push(response)\n      } catch (error) {\n        console.error(`Failed to process input: ${input}`, error)\n        // Continue with other inputs\n      }\n    }\n    \n    return responses\n  }\n\n  /**\n   * Get agent capabilities and specializations\n   */\n  public getAgentCapabilities(agentId: string): {\n    specializations: string[]\n    knowledgeAreas: string[]\n    responseStyle: string\n    qualityThreshold: number\n  } | null {\n    const config = this.agentConfigs.get(agentId)\n    const knowledge = this.knowledgeBase.getAgentKnowledge(agentId)\n    \n    if (!config || !knowledge) return null\n\n    return {\n      specializations: config.specialization,\n      knowledgeAreas: knowledge.specializations,\n      responseStyle: config.responseStyle,\n      qualityThreshold: config.minQualityThreshold\n    }\n  }\n\n  /**\n   * Analyze input without generating full response (for preview/validation)\n   */\n  public analyzeInput(input: string): AdvancedAnalysis {\n    return this.intelligenceLayer.performAdvancedAnalysis(input)\n  }\n\n  private initializeAgentConfigs(): void {\n    this.agentConfigs.set('atendimento', {\n      id: 'atendimento',\n      name: 'Agente de Atendimento',\n      specialization: ['briefing', 'client-communication', 'project-management'],\n      responseStyle: 'comprehensive',\n      minQualityThreshold: 0.8,\n      enableAdvancedAnalysis: true\n    })\n\n    this.agentConfigs.set('planejamento', {\n      id: 'planejamento',\n      name: 'Agente de Planejamento',\n      specialization: ['strategy', 'planning', 'analysis'],\n      responseStyle: 'detailed',\n      minQualityThreshold: 0.85,\n      enableAdvancedAnalysis: true\n    })\n\n    this.agentConfigs.set('midia', {\n      id: 'midia',\n      name: 'Agente de Mídia',\n      specialization: ['performance', 'optimization', 'analytics'],\n      responseStyle: 'detailed',\n      minQualityThreshold: 0.8,\n      enableAdvancedAnalysis: true\n    })\n  }\n\n  private performBasicAnalysis(input: string): AdvancedAnalysis {\n    // Fallback to basic analysis if advanced is disabled\n    const basicAnalysis = this.responseEngine.analyzeInput(input)\n    \n    // Convert to AdvancedAnalysis format with minimal additional data\n    return {\n      ...basicAnalysis,\n      namedEntities: [],\n      topicClusters: [],\n      linguisticFeatures: {\n        readabilityScore: 50,\n        formalityLevel: 'neutral',\n        technicalComplexity: 0.1,\n        emotionalIntensity: 0.1,\n        questionCount: (input.match(/\\?/g) || []).length,\n        imperativeCount: 0,\n        averageSentenceLength: input.split(/[.!?]+/).length > 0 ? \n          input.split(/\\s+/).length / input.split(/[.!?]+/).length : 0\n      },\n      businessContext: {\n        industry: [],\n        businessStage: 'growth',\n        marketingMaturity: 'intermediate',\n        budgetRange: 'small',\n        timeframe: 'medium-term'\n      },\n      riskFactors: [],\n      opportunityIndicators: []\n    }\n  }\n\n  private mergeSections(templateSections: any[], enhancedSections: any[]): any[] {\n    // Merge sections from template and enhanced response, avoiding duplicates\n    const sectionMap = new Map()\n    \n    // Add template sections first (higher priority)\n    templateSections.forEach(section => {\n      sectionMap.set(section.title, section)\n    })\n    \n    // Add enhanced sections if not already present\n    enhancedSections.forEach(section => {\n      if (!sectionMap.has(section.title)) {\n        sectionMap.set(section.title, section)\n      }\n    })\n    \n    // Convert back to array and sort by priority\n    return Array.from(sectionMap.values()).sort((a, b) => (b.priority || 0) - (a.priority || 0))\n  }\n\n  private calculateFinalConfidence(analysis: AdvancedAnalysis, sections: any[]): number {\n    let confidence = analysis.completeness * 0.4 // Base on input completeness\n    \n    // Boost confidence based on number of quality sections\n    confidence += Math.min(sections.length * 0.1, 0.4)\n    \n    // Adjust based on risk factors\n    if (analysis.riskFactors.length > 0) {\n      const avgRiskSeverity = analysis.riskFactors.reduce((sum, risk) => {\n        const severityScore = { low: 0.1, medium: 0.3, high: 0.5, critical: 0.7 }\n        return sum + (severityScore[risk.severity] || 0.3)\n      }, 0) / analysis.riskFactors.length\n      \n      confidence -= avgRiskSeverity * 0.2\n    }\n    \n    return Math.max(0.1, Math.min(1.0, confidence))\n  }\n\n  private calculateFinalCompleteness(analysis: AdvancedAnalysis, sections: any[]): number {\n    let completeness = analysis.completeness * 0.5 // Base on input completeness\n    \n    // Add completeness based on section coverage\n    const requiredSectionTypes = ['analysis', 'recommendation', 'next-steps']\n    const presentTypes = new Set(sections.map(s => s.type))\n    const typesCovered = requiredSectionTypes.filter(type => presentTypes.has(type)).length\n    \n    completeness += (typesCovered / requiredSectionTypes.length) * 0.5\n    \n    return Math.min(1.0, completeness)\n  }\n\n  private formatResponse(response: EnhancedResponse, style: 'concise' | 'detailed' | 'comprehensive'): string {\n    let formatted = `# ${response.summary}\\n\\n`\n    \n    // Filter sections based on style\n    let sectionsToInclude = response.sections\n    \n    if (style === 'concise') {\n      sectionsToInclude = response.sections\n        .filter(s => s.type === 'analysis' || s.type === 'recommendation' || s.type === 'next-steps')\n        .slice(0, 3)\n    } else if (style === 'detailed') {\n      sectionsToInclude = response.sections.slice(0, 6)\n    }\n    // comprehensive includes all sections\n    \n    // Format sections\n    sectionsToInclude.forEach(section => {\n      formatted += `## ${section.title}\\n\\n${section.content}\\n\\n`\n    })\n    \n    // Add follow-up questions for detailed and comprehensive styles\n    if (style !== 'concise' && response.followUpQuestions.length > 0) {\n      formatted += `## 🤔 Perguntas para Aprofundamento\\n\\n`\n      response.followUpQuestions.forEach((question, index) => {\n        formatted += `${index + 1}. ${question}\\n`\n      })\n      formatted += '\\n'\n    }\n    \n    // Add action items\n    if (response.actionItems.length > 0) {\n      formatted += `## ✅ Itens de Ação\\n\\n`\n      response.actionItems.forEach((item, index) => {\n        formatted += `- [ ] ${item}\\n`\n      })\n      formatted += '\\n'\n    }\n    \n    // Add metadata footer for comprehensive style\n    if (style === 'comprehensive') {\n      formatted += `---\\n\\n`\n      formatted += `**Tempo de leitura estimado:** ${response.estimatedReadTime} min | `\n      formatted += `**Confiança:** ${Math.round(response.confidence * 100)}% | `\n      formatted += `**Completude:** ${Math.round(response.completeness * 100)}%\\n`\n    }\n    \n    return formatted\n  }\n\n  private calculateMetadata(\n    analysis: AdvancedAnalysis, \n    response: EnhancedResponse, \n    processingTime: number,\n    config: AgentConfig\n  ): ResponseMetadata {\n    // Calculate quality score based on multiple factors\n    let qualityScore = 0\n    \n    // Content quality (40%)\n    qualityScore += response.completeness * 0.4\n    \n    // Analysis depth (30%)\n    const analysisDepth = (analysis.namedEntities.length > 0 ? 0.1 : 0) +\n                         (analysis.topicClusters.length > 0 ? 0.1 : 0) +\n                         (analysis.riskFactors.length > 0 ? 0.05 : 0) +\n                         (analysis.opportunityIndicators.length > 0 ? 0.05 : 0)\n    qualityScore += Math.min(analysisDepth, 0.3)\n    \n    // Response structure (20%)\n    const structureScore = Math.min(response.sections.length * 0.03, 0.2)\n    qualityScore += structureScore\n    \n    // Actionability (10%)\n    const actionabilityScore = Math.min(response.actionItems.length * 0.02, 0.1)\n    qualityScore += actionabilityScore\n    \n    return {\n      processingTime,\n      confidence: response.confidence,\n      completeness: response.completeness,\n      qualityScore: Math.min(1.0, qualityScore),\n      templateUsed: 'dynamic', // Could be more specific\n      riskFactors: analysis.riskFactors.length,\n      opportunities: analysis.opportunityIndicators.length,\n      followUpRecommended: response.followUpQuestions.length > 0 || analysis.completeness < 0.7\n    }\n  }\n\n  private generateResponseId(): string {\n    return `resp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`\n  }\n}\n\n// Export singleton instance\nexport const enhancedAgentSystem = EnhancedAgentSystem.getInstance()\n\n// Utility function for easy access\nexport async function generateAgentResponse(\n  agentId: string, \n  input: string, \n  userContext?: Record<string, any>\n): Promise<AgentResponse> {\n  return enhancedAgentSystem.generateResponse(agentId, input, userContext)\n}\n\n// Utility function for input analysis\nexport function analyzeUserInput(input: string): AdvancedAnalysis {\n  return enhancedAgentSystem.analyzeInput(input)\n}\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;;;;;;;;AAED;AACA;AACA;AACA;;;;;;AAiCO,MAAM;IAgBX,OAAc,cAAmC;QAC/C,IAAI,CAAC,oBAAoB,QAAQ,EAAE;YACjC,oBAAoB,QAAQ,GAAG,IAAI;QACrC;QACA,OAAO,oBAAoB,QAAQ;IACrC;IAEA;;GAEC,GACD,MAAa,iBAAiB,OAAe,EAAE,KAAa,EAAE,WAAiC,EAA0B;QACvH,MAAM,YAAY,KAAK,GAAG;QAC1B,MAAM,SAAS,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC;QAErC,IAAI,CAAC,QAAQ;YACX,MAAM,IAAI,MAAM,AAAC,sCAA6C,OAAR;QACxD;QAEA,IAAI;YACF,0CAA0C;YAC1C,MAAM,WAAW,OAAO,sBAAsB,GAC1C,IAAI,CAAC,iBAAiB,CAAC,uBAAuB,CAAC,SAC/C,IAAI,CAAC,oBAAoB,CAAC;YAE9B,2CAA2C;YAC3C,MAAM,kBAAmC;gBACvC;gBACA;gBACA;gBACA;YACF;YAEA,MAAM,mBAAmB,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC;YAE9D,2DAA2D;YAC3D,MAAM,mBAAmB,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,SAAS,OAAO;YAE9E,yDAAyD;YACzD,MAAM,iBAAiB,IAAI,CAAC,aAAa,CAAC,kBAAkB,iBAAiB,QAAQ;YAErF,4CAA4C;YAC5C,MAAM,gBAAkC;gBACtC,GAAG,gBAAgB;gBACnB,UAAU;gBACV,YAAY,IAAI,CAAC,wBAAwB,CAAC,UAAU;gBACpD,cAAc,IAAI,CAAC,0BAA0B,CAAC,UAAU;YAC1D;YAEA,6CAA6C;YAC7C,MAAM,kBAAkB,IAAI,CAAC,cAAc,CAAC,eAAe,OAAO,aAAa;YAE/E,6BAA6B;YAC7B,MAAM,iBAAiB,KAAK,GAAG,KAAK;YACpC,MAAM,WAAW,IAAI,CAAC,iBAAiB,CAAC,UAAU,eAAe,gBAAgB;YAEjF,6BAA6B;YAC7B,IAAI,SAAS,YAAY,GAAG,OAAO,mBAAmB,EAAE;gBACtD,QAAQ,IAAI,CAAC,AAAC,8CAAyD,OAAZ,SAAQ,MAA0B,OAAtB,SAAS,YAAY;YAC5F,6CAA6C;YAC/C;YAEA,OAAO;gBACL,IAAI,IAAI,CAAC,kBAAkB;gBAC3B;gBACA;gBACA;gBACA,UAAU;gBACV;gBACA;gBACA,WAAW,IAAI;YACjB;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,AAAC,uCAA8C,OAAR,SAAQ,MAAI;YACjE,MAAM,IAAI,MAAM,AAAC,gCAAwF,OAAzD,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC3F;IACF;IAEA;;GAEC,GACD,MAAa,uBACX,OAAe,EACf,MAAgB,EAChB,WAAiC,EACP;QAC1B,MAAM,YAA6B,EAAE;QAErC,KAAK,MAAM,SAAS,OAAQ;YAC1B,IAAI;gBACF,MAAM,WAAW,MAAM,IAAI,CAAC,gBAAgB,CAAC,SAAS,OAAO;gBAC7D,UAAU,IAAI,CAAC;YACjB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,AAAC,4BAAiC,OAAN,QAAS;YACnD,6BAA6B;YAC/B;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAO,qBAAqB,OAAe,EAKlC;QACP,MAAM,SAAS,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC;QACrC,MAAM,YAAY,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC;QAEvD,IAAI,CAAC,UAAU,CAAC,WAAW,OAAO;QAElC,OAAO;YACL,iBAAiB,OAAO,cAAc;YACtC,gBAAgB,UAAU,eAAe;YACzC,eAAe,OAAO,aAAa;YACnC,kBAAkB,OAAO,mBAAmB;QAC9C;IACF;IAEA;;GAEC,GACD,AAAO,aAAa,KAAa,EAAoB;QACnD,OAAO,IAAI,CAAC,iBAAiB,CAAC,uBAAuB,CAAC;IACxD;IAEQ,yBAA+B;QACrC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,eAAe;YACnC,IAAI;YACJ,MAAM;YACN,gBAAgB;gBAAC;gBAAY;gBAAwB;aAAqB;YAC1E,eAAe;YACf,qBAAqB;YACrB,wBAAwB;QAC1B;QAEA,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,gBAAgB;YACpC,IAAI;YACJ,MAAM;YACN,gBAAgB;gBAAC;gBAAY;gBAAY;aAAW;YACpD,eAAe;YACf,qBAAqB;YACrB,wBAAwB;QAC1B;QAEA,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS;YAC7B,IAAI;YACJ,MAAM;YACN,gBAAgB;gBAAC;gBAAe;gBAAgB;aAAY;YAC5D,eAAe;YACf,qBAAqB;YACrB,wBAAwB;QAC1B;IACF;IAEQ,qBAAqB,KAAa,EAAoB;QAC5D,qDAAqD;QACrD,MAAM,gBAAgB,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC;QAEvD,kEAAkE;QAClE,OAAO;YACL,GAAG,aAAa;YAChB,eAAe,EAAE;YACjB,eAAe,EAAE;YACjB,oBAAoB;gBAClB,kBAAkB;gBAClB,gBAAgB;gBAChB,qBAAqB;gBACrB,oBAAoB;gBACpB,eAAe,CAAC,MAAM,KAAK,CAAC,UAAU,EAAE,EAAE,MAAM;gBAChD,iBAAiB;gBACjB,uBAAuB,MAAM,KAAK,CAAC,UAAU,MAAM,GAAG,IACpD,MAAM,KAAK,CAAC,OAAO,MAAM,GAAG,MAAM,KAAK,CAAC,UAAU,MAAM,GAAG;YAC/D;YACA,iBAAiB;gBACf,UAAU,EAAE;gBACZ,eAAe;gBACf,mBAAmB;gBACnB,aAAa;gBACb,WAAW;YACb;YACA,aAAa,EAAE;YACf,uBAAuB,EAAE;QAC3B;IACF;IAEQ,cAAc,gBAAuB,EAAE,gBAAuB,EAAS;QAC7E,0EAA0E;QAC1E,MAAM,aAAa,IAAI;QAEvB,gDAAgD;QAChD,iBAAiB,OAAO,CAAC,CAAA;YACvB,WAAW,GAAG,CAAC,QAAQ,KAAK,EAAE;QAChC;QAEA,+CAA+C;QAC/C,iBAAiB,OAAO,CAAC,CAAA;YACvB,IAAI,CAAC,WAAW,GAAG,CAAC,QAAQ,KAAK,GAAG;gBAClC,WAAW,GAAG,CAAC,QAAQ,KAAK,EAAE;YAChC;QACF;QAEA,6CAA6C;QAC7C,OAAO,MAAM,IAAI,CAAC,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,EAAE,QAAQ,IAAI,CAAC,IAAI,CAAC,EAAE,QAAQ,IAAI,CAAC;IAC5F;IAEQ,yBAAyB,QAA0B,EAAE,QAAe,EAAU;QACpF,IAAI,aAAa,SAAS,YAAY,GAAG,IAAI,6BAA6B;;QAE1E,uDAAuD;QACvD,cAAc,KAAK,GAAG,CAAC,SAAS,MAAM,GAAG,KAAK;QAE9C,+BAA+B;QAC/B,IAAI,SAAS,WAAW,CAAC,MAAM,GAAG,GAAG;YACnC,MAAM,kBAAkB,SAAS,WAAW,CAAC,MAAM,CAAC,CAAC,KAAK;gBACxD,MAAM,gBAAgB;oBAAE,KAAK;oBAAK,QAAQ;oBAAK,MAAM;oBAAK,UAAU;gBAAI;gBACxE,OAAO,MAAM,CAAC,aAAa,CAAC,KAAK,QAAQ,CAAC,IAAI,GAAG;YACnD,GAAG,KAAK,SAAS,WAAW,CAAC,MAAM;YAEnC,cAAc,kBAAkB;QAClC;QAEA,OAAO,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,KAAK;IACrC;IAEQ,2BAA2B,QAA0B,EAAE,QAAe,EAAU;QACtF,IAAI,eAAe,SAAS,YAAY,GAAG,IAAI,6BAA6B;;QAE5E,6CAA6C;QAC7C,MAAM,uBAAuB;YAAC;YAAY;YAAkB;SAAa;QACzE,MAAM,eAAe,IAAI,IAAI,SAAS,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;QACrD,MAAM,eAAe,qBAAqB,MAAM,CAAC,CAAA,OAAQ,aAAa,GAAG,CAAC,OAAO,MAAM;QAEvF,gBAAgB,AAAC,eAAe,qBAAqB,MAAM,GAAI;QAE/D,OAAO,KAAK,GAAG,CAAC,KAAK;IACvB;IAEQ,eAAe,QAA0B,EAAE,KAA+C,EAAU;QAC1G,IAAI,YAAY,AAAC,KAAqB,OAAjB,SAAS,OAAO,EAAC;QAEtC,iCAAiC;QACjC,IAAI,oBAAoB,SAAS,QAAQ;QAEzC,IAAI,UAAU,WAAW;YACvB,oBAAoB,SAAS,QAAQ,CAClC,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,cAAc,EAAE,IAAI,KAAK,oBAAoB,EAAE,IAAI,KAAK,cAC/E,KAAK,CAAC,GAAG;QACd,OAAO,IAAI,UAAU,YAAY;YAC/B,oBAAoB,SAAS,QAAQ,CAAC,KAAK,CAAC,GAAG;QACjD;QACA,sCAAsC;QAEtC,kBAAkB;QAClB,kBAAkB,OAAO,CAAC,CAAA;YACxB,aAAa,AAAC,MAAyB,OAApB,QAAQ,KAAK,EAAC,QAAsB,OAAhB,QAAQ,OAAO,EAAC;QACzD;QAEA,gEAAgE;QAChE,IAAI,UAAU,aAAa,SAAS,iBAAiB,CAAC,MAAM,GAAG,GAAG;YAChE,aAAc;YACd,SAAS,iBAAiB,CAAC,OAAO,CAAC,CAAC,UAAU;gBAC5C,aAAa,AAAC,GAAgB,OAAd,QAAQ,GAAE,MAAa,OAAT,UAAS;YACzC;YACA,aAAa;QACf;QAEA,mBAAmB;QACnB,IAAI,SAAS,WAAW,CAAC,MAAM,GAAG,GAAG;YACnC,aAAc;YACd,SAAS,WAAW,CAAC,OAAO,CAAC,CAAC,MAAM;gBAClC,aAAa,AAAC,SAAa,OAAL,MAAK;YAC7B;YACA,aAAa;QACf;QAEA,8CAA8C;QAC9C,IAAI,UAAU,iBAAiB;YAC7B,aAAc;YACd,aAAa,AAAC,kCAA4D,OAA3B,SAAS,iBAAiB,EAAC;YAC1E,aAAa,AAAC,kBAAuD,OAAtC,KAAK,KAAK,CAAC,SAAS,UAAU,GAAG,MAAK;YACrE,aAAa,AAAC,mBAA0D,OAAxC,KAAK,KAAK,CAAC,SAAS,YAAY,GAAG,MAAK;QAC1E;QAEA,OAAO;IACT;IAEQ,kBACN,QAA0B,EAC1B,QAA0B,EAC1B,cAAsB,EACtB,MAAmB,EACD;QAClB,oDAAoD;QACpD,IAAI,eAAe;QAEnB,wBAAwB;QACxB,gBAAgB,SAAS,YAAY,GAAG;QAExC,uBAAuB;QACvB,MAAM,gBAAgB,CAAC,SAAS,aAAa,CAAC,MAAM,GAAG,IAAI,MAAM,CAAC,IAC7C,CAAC,SAAS,aAAa,CAAC,MAAM,GAAG,IAAI,MAAM,CAAC,IAC5C,CAAC,SAAS,WAAW,CAAC,MAAM,GAAG,IAAI,OAAO,CAAC,IAC3C,CAAC,SAAS,qBAAqB,CAAC,MAAM,GAAG,IAAI,OAAO,CAAC;QAC1E,gBAAgB,KAAK,GAAG,CAAC,eAAe;QAExC,2BAA2B;QAC3B,MAAM,iBAAiB,KAAK,GAAG,CAAC,SAAS,QAAQ,CAAC,MAAM,GAAG,MAAM;QACjE,gBAAgB;QAEhB,sBAAsB;QACtB,MAAM,qBAAqB,KAAK,GAAG,CAAC,SAAS,WAAW,CAAC,MAAM,GAAG,MAAM;QACxE,gBAAgB;QAEhB,OAAO;YACL;YACA,YAAY,SAAS,UAAU;YAC/B,cAAc,SAAS,YAAY;YACnC,cAAc,KAAK,GAAG,CAAC,KAAK;YAC5B,cAAc;YACd,aAAa,SAAS,WAAW,CAAC,MAAM;YACxC,eAAe,SAAS,qBAAqB,CAAC,MAAM;YACpD,qBAAqB,SAAS,iBAAiB,CAAC,MAAM,GAAG,KAAK,SAAS,YAAY,GAAG;QACxF;IACF;IAEQ,qBAA6B;QACnC,OAAO,AAAC,QAAqB,OAAd,KAAK,GAAG,IAAG,KAA2C,OAAxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;IACpE;IApVA,aAAsB;QANtB,+KAAQ,kBAAR,KAAA;QACA,+KAAQ,qBAAR,KAAA;QACA,+KAAQ,kBAAR,KAAA;QACA,+KAAQ,iBAAR,KAAA;QACA,+KAAQ,gBAAyC,IAAI;QAGnD,IAAI,CAAC,cAAc,GAAG,2JAAc,CAAC,WAAW;QAChD,IAAI,CAAC,iBAAiB,GAAG,iKAAiB,CAAC,WAAW;QACtD,IAAI,CAAC,cAAc,GAAG,sKAAsB,CAAC,WAAW;QACxD,IAAI,CAAC,aAAa,GAAG,8JAAkB,CAAC,WAAW;QACnD,IAAI,CAAC,sBAAsB;IAC7B;AA+UF;AA5VE,yKADW,qBACI,YAAf,KAAA;AA+VK,MAAM,sBAAsB,oBAAoB,WAAW;AAG3D,eAAe,sBACpB,OAAe,EACf,KAAa,EACb,WAAiC;IAEjC,OAAO,oBAAoB,gBAAgB,CAAC,SAAS,OAAO;AAC9D;AAGO,SAAS,iBAAiB,KAAa;IAC5C,OAAO,oBAAoB,YAAY,CAAC;AAC1C", "debugId": null}}, {"offset": {"line": 3382, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent3/agentepub/src/app/dashboard/agentes/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport ProtectedRoute from '@/components/auth/ProtectedRoute'\nimport DashboardLayout from '@/components/dashboard/DashboardLayout'\nimport {\n  UserGroupIcon,\n  CalendarDaysIcon,\n  ChartBarIcon,\n  SparklesIcon,\n  DocumentTextIcon,\n  ArrowRightIcon,\n  ClipboardDocumentIcon\n} from '@heroicons/react/24/outline'\nimport { generateAgentResponse, analyzeUserInput } from '@/lib/agents/enhancedAgentSystem'\nimport type { AgentResponse, AdvancedAnalysis } from '@/lib/agents/enhancedAgentSystem'\n\nconst agents = [\n  {\n    id: 'atendimento',\n    name: 'Agente de Atendimento',\n    description: 'Organiza briefings, acompanha prazos e mantém a comunicação com clientes sempre em dia.',\n    icon: UserGroupIcon,\n    color: 'blue',\n    capabilities: [\n      'Estrutura briefings automaticamente',\n      'Cria cronogramas de aprovação',\n      'Monitora prazos e entregas',\n      'Gera relatórios de status'\n    ],\n    inputPlaceholder: 'Cole aqui o briefing do cliente ou descreva a demanda...',\n    examples: [\n      'Briefing: Campanha para lançamento de produto X, público jovem 18-25 anos, budget R$ 50k',\n      'Cliente solicitou alterações no cronograma da campanha Y',\n      'Preciso organizar as aprovações para a campanha de Black Friday'\n    ]\n  },\n  {\n    id: 'planejamento',\n    name: 'Agente de Planejamento',\n    description: 'Gera cronogramas inteligentes, insights estratégicos e organiza toda a execução da campanha.',\n    icon: CalendarDaysIcon,\n    color: 'green',\n    capabilities: [\n      'Cronogramas automáticos',\n      'Análise de concorrência',\n      'Sugestões de estratégia',\n      'Distribuição de budget'\n    ],\n    inputPlaceholder: 'Descreva o projeto ou campanha que precisa ser planejada...',\n    examples: [\n      'Campanha de 60 dias para lançamento de app, budget R$ 100k, 3 fases',\n      'Planejamento estratégico para Black Friday, múltiplos canais',\n      'Cronograma para campanha institucional com prazo apertado'\n    ]\n  },\n  {\n    id: 'midia',\n    name: 'Agente de Mídia',\n    description: 'Analisa performance, sugere otimizações e gera relatórios com insights acionáveis.',\n    icon: ChartBarIcon,\n    color: 'purple',\n    capabilities: [\n      'Análise de KPIs',\n      'Sugestões de otimização',\n      'Relatórios automatizados',\n      'Alertas de performance'\n    ],\n    inputPlaceholder: 'Cole dados de performance ou descreva a campanha para análise...',\n    examples: [\n      'CTR: 2.1%, CPC: R$ 0.85, Budget gasto: R$ 15k de R$ 30k',\n      'Campanha Instagram com baixo engajamento, preciso otimizar',\n      'Análise de performance da campanha de verão nos últimos 30 dias'\n    ]\n  }\n]\n\nexport default function AgentesPage() {\n  const [selectedAgent, setSelectedAgent] = useState<string | null>(null)\n  const [input, setInput] = useState('')\n  const [isProcessing, setIsProcessing] = useState(false)\n  const [result, setResult] = useState<string | null>(null)\n  const [agentResponse, setAgentResponse] = useState<AgentResponse | null>(null)\n  const [inputAnalysis, setInputAnalysis] = useState<AdvancedAnalysis | null>(null)\n  const [showAnalysis, setShowAnalysis] = useState(false)\n  const [copySuccess, setCopySuccess] = useState(false)\n\n  const currentAgent = agents.find(agent => agent.id === selectedAgent)\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    if (!input.trim() || !currentAgent) return\n\n    setIsProcessing(true)\n    setResult(null)\n    setAgentResponse(null)\n\n    try {\n      // Analyze input first for preview\n      const analysis = analyzeUserInput(input)\n      setInputAnalysis(analysis)\n\n      // Generate enhanced response\n      const response = await generateAgentResponse(currentAgent.id, input)\n      setAgentResponse(response)\n      setResult(response.formattedOutput)\n    } catch (error) {\n      console.error('Error generating response:', error)\n      setResult('Erro ao processar solicitação. Tente novamente.')\n    } finally {\n      setIsProcessing(false)\n    }\n  }\n\n  const handleAnalyzeInput = () => {\n    if (!input.trim()) return\n\n    const analysis = analyzeUserInput(input)\n    setInputAnalysis(analysis)\n    setShowAnalysis(true)\n  }\n\n  const handleCopyResponse = async () => {\n    if (!result) return\n\n    try {\n      await navigator.clipboard.writeText(result)\n      setCopySuccess(true)\n      setTimeout(() => setCopySuccess(false), 2000)\n    } catch (error) {\n      console.error('Erro ao copiar:', error)\n    }\n  }\n\n  const resetAgent = () => {\n    setSelectedAgent(null)\n    setInput('')\n    setResult(null)\n    setAgentResponse(null)\n    setInputAnalysis(null)\n    setShowAnalysis(false)\n    setCopySuccess(false)\n  }\n\n  if (!selectedAgent) {\n    return (\n      <ProtectedRoute>\n        <DashboardLayout>\n          <div className=\"space-y-6\">\n            <div>\n              <h1 className=\"text-2xl font-bold text-gray-900\">Agentes Inteligentes</h1>\n              <p className=\"text-gray-600\">Escolha um agente especializado para ajudar com sua tarefa</p>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n              {agents.map((agent) => (\n                <div key={agent.id} className=\"bg-white rounded-lg shadow hover:shadow-md transition-shadow cursor-pointer\" onClick={() => setSelectedAgent(agent.id)}>\n                  <div className=\"p-6\">\n                    <div className=\"flex items-center gap-3 mb-4\">\n                      <div className={\n                        agent.id === 'atendimento' ? 'bg-blue-100 p-3 rounded-lg' :\n                          agent.id === 'planejamento' ? 'bg-green-100 p-3 rounded-lg' :\n                            'bg-purple-100 p-3 rounded-lg'\n                      }>\n                        <agent.icon className={\n                          agent.id === 'atendimento' ? 'h-6 w-6 text-blue-600' :\n                            agent.id === 'planejamento' ? 'h-6 w-6 text-green-600' :\n                              'h-6 w-6 text-purple-600'\n                        } />\n                      </div>\n                      <h3 className=\"font-semibold text-gray-900\">{agent.name}</h3>\n                    </div>\n\n                    <p className=\"text-gray-600 mb-4\">{agent.description}</p>\n\n                    <div className=\"space-y-2 mb-6\">\n                      {agent.capabilities.map((capability, idx) => (\n                        <div key={idx} className=\"flex items-center gap-2\">\n                          <div className=\"w-1.5 h-1.5 bg-primary-500 rounded-full\"></div>\n                          <span className=\"text-sm text-gray-700\">{capability}</span>\n                        </div>\n                      ))}\n                    </div>\n\n                    <button className=\"w-full bg-blue-600 text-white py-3 px-4 rounded-md font-semibold hover:bg-blue-700 transition-colors flex items-center justify-center gap-2\">\n                      🚀 Usar Agente\n                      <ArrowRightIcon className=\"h-5 w-5\" />\n                    </button>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </DashboardLayout>\n      </ProtectedRoute>\n    )\n  }\n\n  return (\n    <ProtectedRoute>\n      <DashboardLayout>\n        <div className=\"space-y-6\">\n          {/* Header */}\n          <div className=\"flex items-center gap-4\">\n            <button\n              onClick={resetAgent}\n              className=\"text-gray-500 hover:text-gray-700\"\n            >\n              ← Voltar\n            </button>\n            <div className=\"flex items-center gap-3\">\n              <div className={\n                currentAgent?.id === 'atendimento' ? 'bg-blue-100 p-2 rounded-lg' :\n                  currentAgent?.id === 'planejamento' ? 'bg-green-100 p-2 rounded-lg' :\n                    'bg-purple-100 p-2 rounded-lg'\n              }>\n                {currentAgent?.icon && <currentAgent.icon className={\n                  currentAgent?.id === 'atendimento' ? 'h-5 w-5 text-blue-600' :\n                    currentAgent?.id === 'planejamento' ? 'h-5 w-5 text-green-600' :\n                      'h-5 w-5 text-purple-600'\n                } />}\n              </div>\n              <div>\n                <h1 className=\"text-xl font-bold text-gray-900\">{currentAgent?.name}</h1>\n                <p className=\"text-sm text-gray-600\">{currentAgent?.description}</p>\n              </div>\n            </div>\n          </div>\n\n          {!result ? (\n            <div className=\"bg-white rounded-lg shadow p-6\">\n              <form onSubmit={handleSubmit} className=\"space-y-6\">\n                <div>\n                  <label htmlFor=\"input\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Descreva sua demanda\n                  </label>\n                  <textarea\n                    id=\"input\"\n                    value={input}\n                    onChange={(e) => setInput(e.target.value)}\n                    rows={6}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none\"\n                    placeholder={currentAgent?.inputPlaceholder}\n                    required\n                  />\n                </div>\n\n                <div className=\"flex gap-3\">\n                  <button\n                    type=\"button\"\n                    onClick={handleAnalyzeInput}\n                    disabled={!input.trim()}\n                    className=\"flex-1 bg-gray-100 text-gray-700 py-3 px-4 rounded-md font-medium hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all flex items-center justify-center gap-2\"\n                  >\n                    🔍 Analisar Input\n                  </button>\n\n                  <button\n                    type=\"submit\"\n                    disabled={isProcessing || !input.trim()}\n                    className=\"flex-2 bg-blue-600 text-white py-3 px-6 rounded-md font-semibold hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all flex items-center justify-center gap-2 border-2 border-blue-600\"\n                    style={{ minWidth: '200px' }}\n                  >\n                    {isProcessing ? (\n                      <>\n                        <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-white\"></div>\n                        ⏳ Processando...\n                      </>\n                    ) : (\n                      <>\n                        <SparklesIcon className=\"h-5 w-5\" />\n                        🚀 Processar\n                      </>\n                    )}\n                  </button>\n                </div>\n              </form>\n\n              {/* Examples */}\n              <div className=\"mt-8 pt-6 border-t border-gray-200\">\n                <h3 className=\"text-sm font-medium text-gray-900 mb-3\">💡 Exemplos:</h3>\n                <div className=\"space-y-2\">\n                  {currentAgent?.examples.map((example, idx) => (\n                    <button\n                      key={idx}\n                      onClick={() => setInput(example)}\n                      className=\"text-left text-sm text-blue-600 hover:text-blue-700 block w-full p-2 rounded hover:bg-blue-50 transition-colors\"\n                    >\n                      💡 {example}\n                    </button>\n                  ))}\n                </div>\n              </div>\n            </div>\n          ) : (\n            <div className=\"space-y-6\">\n              {/* Input Analysis Preview */}\n              {inputAnalysis && showAnalysis && (\n                <div className=\"bg-blue-50 rounded-lg border border-blue-200 p-4\">\n                  <div className=\"flex items-center justify-between mb-3\">\n                    <h3 className=\"text-sm font-semibold text-blue-900\">Análise do Input</h3>\n                    <button\n                      onClick={() => setShowAnalysis(false)}\n                      className=\"text-blue-600 hover:text-blue-800 text-sm\"\n                    >\n                      Ocultar\n                    </button>\n                  </div>\n                  <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm\">\n                    <div>\n                      <span className=\"text-blue-700 font-medium\">Complexidade:</span>\n                      <span className=\"ml-1 text-blue-900 capitalize\">{inputAnalysis.complexity}</span>\n                    </div>\n                    <div>\n                      <span className=\"text-blue-700 font-medium\">Urgência:</span>\n                      <span className=\"ml-1 text-blue-900 capitalize\">{inputAnalysis.urgency}</span>\n                    </div>\n                    <div>\n                      <span className=\"text-blue-700 font-medium\">Completude:</span>\n                      <span className=\"ml-1 text-blue-900\">{Math.round(inputAnalysis.completeness * 100)}%</span>\n                    </div>\n                    <div>\n                      <span className=\"text-blue-700 font-medium\">Sentimento:</span>\n                      <span className=\"ml-1 text-blue-900 capitalize\">{inputAnalysis.sentiment}</span>\n                    </div>\n                  </div>\n                  {inputAnalysis.riskFactors.length > 0 && (\n                    <div className=\"mt-3 pt-3 border-t border-blue-200\">\n                      <span className=\"text-blue-700 font-medium text-sm\">Riscos Identificados:</span>\n                      <span className=\"ml-1 text-red-600 text-sm\">{inputAnalysis.riskFactors.length} fatores</span>\n                    </div>\n                  )}\n                </div>\n              )}\n\n              <div className=\"bg-white rounded-lg shadow\">\n                {/* Response Quality Metrics */}\n                {agentResponse && (\n                  <div className=\"border-b border-gray-200 p-4\">\n                    <div className=\"flex items-center justify-between mb-3\">\n                      <h3 className=\"text-sm font-semibold text-gray-900\">Métricas de Qualidade</h3>\n                      <div className=\"flex items-center gap-4 text-xs text-gray-600\">\n                        <span>Processado em {agentResponse.metadata.processingTime}ms</span>\n                        <span>Tempo de leitura: {agentResponse.response.estimatedReadTime} min</span>\n                      </div>\n                    </div>\n                    <div className=\"grid grid-cols-3 gap-4\">\n                      <div className=\"text-center\">\n                        <div className=\"text-lg font-bold text-green-600\">\n                          {Math.round(agentResponse.metadata.confidence * 100)}%\n                        </div>\n                        <div className=\"text-xs text-gray-600\">Confiança</div>\n                      </div>\n                      <div className=\"text-center\">\n                        <div className=\"text-lg font-bold text-blue-600\">\n                          {Math.round(agentResponse.metadata.completeness * 100)}%\n                        </div>\n                        <div className=\"text-xs text-gray-600\">Completude</div>\n                      </div>\n                      <div className=\"text-center\">\n                        <div className=\"text-lg font-bold text-purple-600\">\n                          {Math.round(agentResponse.metadata.qualityScore * 100)}%\n                        </div>\n                        <div className=\"text-xs text-gray-600\">Qualidade</div>\n                      </div>\n                    </div>\n                    {agentResponse.metadata.followUpRecommended && (\n                      <div className=\"mt-3 p-2 bg-yellow-50 border border-yellow-200 rounded text-sm text-yellow-800\">\n                        💡 Recomendamos uma conversa de acompanhamento para esclarecer detalhes adicionais.\n                      </div>\n                    )}\n                  </div>\n                )}\n\n                <div className=\"p-6\">\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <div className=\"flex items-center gap-2\">\n                      <DocumentTextIcon className=\"h-5 w-5 text-green-500\" />\n                      <h2 className=\"text-lg font-semibold text-gray-900\">Resultado Processado</h2>\n                    </div>\n                    <button\n                      onClick={handleCopyResponse}\n                      className=\"flex items-center gap-2 px-3 py-2 text-sm bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-md transition-colors\"\n                    >\n                      <ClipboardDocumentIcon className=\"h-4 w-4\" />\n                      {copySuccess ? 'Copiado!' : 'Copiar Resposta'}\n                    </button>\n                  </div>\n\n                  <div className=\"prose max-w-none\">\n                    <pre className=\"whitespace-pre-wrap text-sm text-gray-700 font-sans leading-relaxed\">\n                      {result}\n                    </pre>\n                  </div>\n\n                  {/* Action Items */}\n                  {agentResponse && agentResponse.response.actionItems.length > 0 && (\n                    <div className=\"mt-6 p-4 bg-gray-50 rounded-lg\">\n                      <h3 className=\"text-sm font-semibold text-gray-900 mb-3\">Itens de Ação Identificados</h3>\n                      <ul className=\"space-y-2\">\n                        {agentResponse.response.actionItems.map((item, index) => (\n                          <li key={index} className=\"flex items-start gap-2 text-sm text-gray-700\">\n                            <span className=\"text-blue-500 mt-1\">•</span>\n                            {item}\n                          </li>\n                        ))}\n                      </ul>\n                    </div>\n                  )}\n\n                  {/* Follow-up Questions */}\n                  {agentResponse && agentResponse.response.followUpQuestions.length > 0 && (\n                    <div className=\"mt-4 p-4 bg-blue-50 rounded-lg\">\n                      <h3 className=\"text-sm font-semibold text-blue-900 mb-3\">Perguntas para Aprofundamento</h3>\n                      <ul className=\"space-y-2\">\n                        {agentResponse.response.followUpQuestions.map((question, index) => (\n                          <li key={index} className=\"text-sm text-blue-800\">\n                            {index + 1}. {question}\n                          </li>\n                        ))}\n                      </ul>\n                    </div>\n                  )}\n                </div>\n              </div>\n\n              <div className=\"flex gap-4\">\n                <button\n                  onClick={() => {\n                    setResult(null)\n                    setInput('')\n                  }}\n                  className=\"flex-1 bg-white text-gray-700 py-2 px-4 rounded-md font-medium border border-gray-300 hover:bg-gray-50 transition-colors\"\n                >\n                  Nova Consulta\n                </button>\n                <button className=\"flex-1 bg-green-600 text-white py-2 px-4 rounded-md font-medium hover:bg-green-700 transition-colors\">\n                  💾 Salvar em Campanha\n                </button>\n              </div>\n            </div>\n          )}\n        </div>\n      </DashboardLayout>\n    </ProtectedRoute>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;;;AAdA;;;;;;AAiBA,MAAM,SAAS;IACb;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM,6OAAa;QACnB,OAAO;QACP,cAAc;YACZ;YACA;YACA;YACA;SACD;QACD,kBAAkB;QAClB,UAAU;YACR;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM,sPAAgB;QACtB,OAAO;QACP,cAAc;YACZ;YACA;YACA;YACA;SACD;QACD,kBAAkB;QAClB,UAAU;YACR;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM,0OAAY;QAClB,OAAO;QACP,cAAc;YACZ;YACA;YACA;YACA;SACD;QACD,kBAAkB;QAClB,UAAU;YACR;YACA;YACA;SACD;IACH;CACD;AAEc,SAAS;;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,yKAAQ,EAAgB;IAClE,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,yKAAQ,EAAC;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,yKAAQ,EAAC;IACjD,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,yKAAQ,EAAgB;IACpD,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,yKAAQ,EAAuB;IACzE,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,yKAAQ,EAA0B;IAC5E,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,yKAAQ,EAAC;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,yKAAQ,EAAC;IAE/C,MAAM,eAAe,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IAEvD,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,cAAc;QAEpC,gBAAgB;QAChB,UAAU;QACV,iBAAiB;QAEjB,IAAI;YACF,kCAAkC;YAClC,MAAM,WAAW,IAAA,kKAAgB,EAAC;YAClC,iBAAiB;YAEjB,6BAA6B;YAC7B,MAAM,WAAW,MAAM,IAAA,uKAAqB,EAAC,aAAa,EAAE,EAAE;YAC9D,iBAAiB;YACjB,UAAU,SAAS,eAAe;QACpC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,UAAU;QACZ,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI,CAAC,MAAM,IAAI,IAAI;QAEnB,MAAM,WAAW,IAAA,kKAAgB,EAAC;QAClC,iBAAiB;QACjB,gBAAgB;IAClB;IAEA,MAAM,qBAAqB;QACzB,IAAI,CAAC,QAAQ;QAEb,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,eAAe;YACf,WAAW,IAAM,eAAe,QAAQ;QAC1C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;QACnC;IACF;IAEA,MAAM,aAAa;QACjB,iBAAiB;QACjB,SAAS;QACT,UAAU;QACV,iBAAiB;QACjB,iBAAiB;QACjB,gBAAgB;QAChB,eAAe;IACjB;IAEA,IAAI,CAAC,eAAe;QAClB,qBACE,6LAAC,0JAAc;sBACb,cAAA,6LAAC,gKAAe;0BACd,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;sCAG/B,6LAAC;4BAAI,WAAU;sCACZ,OAAO,GAAG,CAAC,CAAC,sBACX,6LAAC;oCAAmB,WAAU;oCAA8E,SAAS,IAAM,iBAAiB,MAAM,EAAE;8CAClJ,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WACH,MAAM,EAAE,KAAK,gBAAgB,+BAC3B,MAAM,EAAE,KAAK,iBAAiB,gCAC5B;kEAEJ,cAAA,6LAAC,MAAM,IAAI;4DAAC,WACV,MAAM,EAAE,KAAK,gBAAgB,0BAC3B,MAAM,EAAE,KAAK,iBAAiB,2BAC5B;;;;;;;;;;;kEAGR,6LAAC;wDAAG,WAAU;kEAA+B,MAAM,IAAI;;;;;;;;;;;;0DAGzD,6LAAC;gDAAE,WAAU;0DAAsB,MAAM,WAAW;;;;;;0DAEpD,6LAAC;gDAAI,WAAU;0DACZ,MAAM,YAAY,CAAC,GAAG,CAAC,CAAC,YAAY,oBACnC,6LAAC;wDAAc,WAAU;;0EACvB,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAK,WAAU;0EAAyB;;;;;;;uDAFjC;;;;;;;;;;0DAOd,6LAAC;gDAAO,WAAU;;oDAA8I;kEAE9J,6LAAC,gPAAc;wDAAC,WAAU;;;;;;;;;;;;;;;;;;mCA9BtB,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;IAwChC;IAEA,qBACE,6LAAC,0JAAc;kBACb,cAAA,6LAAC,gKAAe;sBACd,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WACH,CAAA,yBAAA,mCAAA,aAAc,EAAE,MAAK,gBAAgB,+BACnC,CAAA,yBAAA,mCAAA,aAAc,EAAE,MAAK,iBAAiB,gCACpC;kDAEH,CAAA,yBAAA,mCAAA,aAAc,IAAI,mBAAI,6LAAC,aAAa,IAAI;4CAAC,WACxC,CAAA,yBAAA,mCAAA,aAAc,EAAE,MAAK,gBAAgB,0BACnC,CAAA,yBAAA,mCAAA,aAAc,EAAE,MAAK,iBAAiB,2BACpC;;;;;;;;;;;kDAGR,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAmC,yBAAA,mCAAA,aAAc,IAAI;;;;;;0DACnE,6LAAC;gDAAE,WAAU;0DAAyB,yBAAA,mCAAA,aAAc,WAAW;;;;;;;;;;;;;;;;;;;;;;;;oBAKpE,CAAC,uBACA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,UAAU;gCAAc,WAAU;;kDACtC,6LAAC;;0DACC,6LAAC;gDAAM,SAAQ;gDAAQ,WAAU;0DAA+C;;;;;;0DAGhF,6LAAC;gDACC,IAAG;gDACH,OAAO;gDACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gDACxC,MAAM;gDACN,WAAU;gDACV,WAAW,EAAE,yBAAA,mCAAA,aAAc,gBAAgB;gDAC3C,QAAQ;;;;;;;;;;;;kDAIZ,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,SAAS;gDACT,UAAU,CAAC,MAAM,IAAI;gDACrB,WAAU;0DACX;;;;;;0DAID,6LAAC;gDACC,MAAK;gDACL,UAAU,gBAAgB,CAAC,MAAM,IAAI;gDACrC,WAAU;gDACV,OAAO;oDAAE,UAAU;gDAAQ;0DAE1B,6BACC;;sEACE,6LAAC;4DAAI,WAAU;;;;;;wDAAkE;;iFAInF;;sEACE,6LAAC,0OAAY;4DAAC,WAAU;;;;;;wDAAY;;;;;;;;;;;;;;;;;;;;0CAS9C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,6LAAC;wCAAI,WAAU;kDACZ,yBAAA,mCAAA,aAAc,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,oBACpC,6LAAC;gDAEC,SAAS,IAAM,SAAS;gDACxB,WAAU;;oDACX;oDACK;;+CAJC;;;;;;;;;;;;;;;;;;;;;6CAWf,6LAAC;wBAAI,WAAU;;4BAEZ,iBAAiB,8BAChB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAsC;;;;;;0DACpD,6LAAC;gDACC,SAAS,IAAM,gBAAgB;gDAC/B,WAAU;0DACX;;;;;;;;;;;;kDAIH,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAK,WAAU;kEAA4B;;;;;;kEAC5C,6LAAC;wDAAK,WAAU;kEAAiC,cAAc,UAAU;;;;;;;;;;;;0DAE3E,6LAAC;;kEACC,6LAAC;wDAAK,WAAU;kEAA4B;;;;;;kEAC5C,6LAAC;wDAAK,WAAU;kEAAiC,cAAc,OAAO;;;;;;;;;;;;0DAExE,6LAAC;;kEACC,6LAAC;wDAAK,WAAU;kEAA4B;;;;;;kEAC5C,6LAAC;wDAAK,WAAU;;4DAAsB,KAAK,KAAK,CAAC,cAAc,YAAY,GAAG;4DAAK;;;;;;;;;;;;;0DAErF,6LAAC;;kEACC,6LAAC;wDAAK,WAAU;kEAA4B;;;;;;kEAC5C,6LAAC;wDAAK,WAAU;kEAAiC,cAAc,SAAS;;;;;;;;;;;;;;;;;;oCAG3E,cAAc,WAAW,CAAC,MAAM,GAAG,mBAClC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAoC;;;;;;0DACpD,6LAAC;gDAAK,WAAU;;oDAA6B,cAAc,WAAW,CAAC,MAAM;oDAAC;;;;;;;;;;;;;;;;;;;0CAMtF,6LAAC;gCAAI,WAAU;;oCAEZ,+BACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAsC;;;;;;kEACpD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;oEAAK;oEAAe,cAAc,QAAQ,CAAC,cAAc;oEAAC;;;;;;;0EAC3D,6LAAC;;oEAAK;oEAAmB,cAAc,QAAQ,CAAC,iBAAiB;oEAAC;;;;;;;;;;;;;;;;;;;0DAGtE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;oEACZ,KAAK,KAAK,CAAC,cAAc,QAAQ,CAAC,UAAU,GAAG;oEAAK;;;;;;;0EAEvD,6LAAC;gEAAI,WAAU;0EAAwB;;;;;;;;;;;;kEAEzC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;oEACZ,KAAK,KAAK,CAAC,cAAc,QAAQ,CAAC,YAAY,GAAG;oEAAK;;;;;;;0EAEzD,6LAAC;gEAAI,WAAU;0EAAwB;;;;;;;;;;;;kEAEzC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;oEACZ,KAAK,KAAK,CAAC,cAAc,QAAQ,CAAC,YAAY,GAAG;oEAAK;;;;;;;0EAEzD,6LAAC;gEAAI,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;4CAG1C,cAAc,QAAQ,CAAC,mBAAmB,kBACzC,6LAAC;gDAAI,WAAU;0DAAiF;;;;;;;;;;;;kDAOtG,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,sPAAgB;gEAAC,WAAU;;;;;;0EAC5B,6LAAC;gEAAG,WAAU;0EAAsC;;;;;;;;;;;;kEAEtD,6LAAC;wDACC,SAAS;wDACT,WAAU;;0EAEV,6LAAC,qQAAqB;gEAAC,WAAU;;;;;;4DAChC,cAAc,aAAa;;;;;;;;;;;;;0DAIhC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DACZ;;;;;;;;;;;4CAKJ,iBAAiB,cAAc,QAAQ,CAAC,WAAW,CAAC,MAAM,GAAG,mBAC5D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAA2C;;;;;;kEACzD,6LAAC;wDAAG,WAAU;kEACX,cAAc,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC7C,6LAAC;gEAAe,WAAU;;kFACxB,6LAAC;wEAAK,WAAU;kFAAqB;;;;;;oEACpC;;+DAFM;;;;;;;;;;;;;;;;4CAUhB,iBAAiB,cAAc,QAAQ,CAAC,iBAAiB,CAAC,MAAM,GAAG,mBAClE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAA2C;;;;;;kEACzD,6LAAC;wDAAG,WAAU;kEACX,cAAc,QAAQ,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,UAAU,sBACvD,6LAAC;gEAAe,WAAU;;oEACvB,QAAQ;oEAAE;oEAAG;;+DADP;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAUrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS;4CACP,UAAU;4CACV,SAAS;wCACX;wCACA,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCAAO,WAAU;kDAAuG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUzI;GAjXwB;KAAA", "debugId": null}}]}