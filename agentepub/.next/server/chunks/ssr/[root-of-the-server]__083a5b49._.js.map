{"version": 3, "sources": [], "sections": [{"offset": {"line": 22, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent3/agentepub/src/components/auth/ProtectedRoute.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/contexts/AuthContext'\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode\n  redirectTo?: string\n}\n\nexport default function ProtectedRoute({ children, redirectTo = '/' }: ProtectedRouteProps) {\n  const { user, loading } = useAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (!loading && !user) {\n      router.push(redirectTo)\n    }\n  }, [user, loading, router, redirectTo])\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"></div>\n      </div>\n    )\n  }\n\n  if (!user) {\n    return null\n  }\n\n  return <>{children}</>\n}\n\n// Loading component for better UX\nexport function LoadingSpinner({ size = 'md' }: { size?: 'sm' | 'md' | 'lg' }) {\n  const sizeClasses = {\n    sm: 'h-4 w-4',\n    md: 'h-8 w-8',\n    lg: 'h-12 w-12'\n  }\n\n  return (\n    <div className=\"flex items-center justify-center\">\n      <div className={`animate-spin rounded-full border-b-2 border-primary-600 ${sizeClasses[size]}`}></div>\n    </div>\n  )\n}\n\n// Auth guard hook for pages\nexport function useAuthGuard(redirectTo = '/') {\n  const { user, loading } = useAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (!loading && !user) {\n      router.push(redirectTo)\n    }\n  }, [user, loading, router, redirectTo])\n\n  return { user, loading, isAuthenticated: !!user }\n}\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AACA;AAJA;;;;;AAWe,SAAS,eAAe,EAAE,QAAQ,EAAE,aAAa,GAAG,EAAuB;IACxF,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,IAAA,0IAAO;IACjC,MAAM,SAAS,IAAA,+IAAS;IAExB,IAAA,kNAAS,EAAC;QACR,IAAI,CAAC,WAAW,CAAC,MAAM;YACrB,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAM;QAAS;QAAQ;KAAW;IAEtC,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBAAO;kBAAG;;AACZ;AAGO,SAAS,eAAe,EAAE,OAAO,IAAI,EAAiC;IAC3E,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAW,CAAC,wDAAwD,EAAE,WAAW,CAAC,KAAK,EAAE;;;;;;;;;;;AAGpG;AAGO,SAAS,aAAa,aAAa,GAAG;IAC3C,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,IAAA,0IAAO;IACjC,MAAM,SAAS,IAAA,+IAAS;IAExB,IAAA,kNAAS,EAAC;QACR,IAAI,CAAC,WAAW,CAAC,MAAM;YACrB,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAM;QAAS;QAAQ;KAAW;IAEtC,OAAO;QAAE;QAAM;QAAS,iBAAiB,CAAC,CAAC;IAAK;AAClD", "debugId": null}}, {"offset": {"line": 119, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent3/agentepub/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatDate(date: string | Date) {\n  return new Intl.DateTimeFormat('pt-BR', {\n    day: '2-digit',\n    month: '2-digit',\n    year: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(new Date(date))\n}\n\nexport function formatCurrency(amount: number) {\n  return new Intl.NumberFormat('pt-BR', {\n    style: 'currency',\n    currency: 'BRL',\n  }).format(amount)\n}\n\nexport function getAreaDisplayName(area: string) {\n  const areaNames = {\n    atendimento: 'Atendimento',\n    planejamento: 'Planejamento',\n    midia: 'Mídia'\n  }\n  return areaNames[area as keyof typeof areaNames] || area\n}\n\nexport function generateSlug(text: string) {\n  return text\n    .toLowerCase()\n    .normalize('NFD')\n    .replace(/[\\u0300-\\u036f]/g, '')\n    .replace(/[^a-z0-9\\s-]/g, '')\n    .replace(/\\s+/g, '-')\n    .replace(/-+/g, '-')\n    .trim()\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,sKAAO,EAAC,IAAA,6IAAI,EAAC;AACtB;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,KAAK;QACL,OAAO;QACP,MAAM;QACN,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAEO,SAAS,eAAe,MAAc;IAC3C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,mBAAmB,IAAY;IAC7C,MAAM,YAAY;QAChB,aAAa;QACb,cAAc;QACd,OAAO;IACT;IACA,OAAO,SAAS,CAAC,KAA+B,IAAI;AACtD;AAEO,SAAS,aAAa,IAAY;IACvC,OAAO,KACJ,WAAW,GACX,SAAS,CAAC,OACV,OAAO,CAAC,oBAAoB,IAC5B,OAAO,CAAC,iBAAiB,IACzB,OAAO,CAAC,QAAQ,KAChB,OAAO,CAAC,OAAO,KACf,IAAI;AACT", "debugId": null}}, {"offset": {"line": 168, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent3/agentepub/src/components/dashboard/DashboardLayout.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { cn } from '@/lib/utils'\nimport {\n  Bars3Icon,\n  XMarkIcon,\n  HomeIcon,\n  FolderIcon,\n  UserGroupIcon,\n  DocumentChartBarIcon,\n  ChatBubbleLeftRightIcon,\n  Cog6ToothIcon,\n  ArrowRightOnRectangleIcon\n} from '@heroicons/react/24/outline'\n\nconst navigation = [\n  { name: 'Dashboard', href: '/dashboard', icon: HomeIcon },\n  { name: 'Campanhas', href: '/dashboard/campanhas', icon: FolderIcon },\n  { name: 'Agent<PERSON>', href: '/dashboard/agentes', icon: UserGroupIcon },\n  { name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', href: '/dashboard/relatorios', icon: DocumentChartBarIcon },\n  { name: 'Chat', href: '/dashboard/chat', icon: ChatBubbleLeftRightIcon },\n  { name: 'Configura<PERSON>õ<PERSON>', href: '/dashboard/configuracoes', icon: Cog6ToothIcon },\n]\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode\n}\n\nexport default function DashboardLayout({ children }: DashboardLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false)\n  const pathname = usePathname()\n  const { user, profile, signOut } = useAuth()\n\n  const handleSignOut = async () => {\n    await signOut()\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Mobile sidebar */}\n      {sidebarOpen && (\n        <div className=\"fixed inset-0 z-50 lg:hidden\">\n          <div className=\"fixed inset-0 bg-gray-600 bg-opacity-75\" onClick={() => setSidebarOpen(false)} />\n          <div className=\"fixed inset-y-0 left-0 flex w-full max-w-xs flex-col bg-white shadow-xl\">\n            <div className=\"flex h-16 items-center justify-between px-6 border-b border-gray-200\">\n              <Link href=\"/dashboard\" className=\"text-xl font-bold text-primary-600\">\n                AgentePub\n              </Link>\n              <button\n                onClick={() => setSidebarOpen(false)}\n                className=\"text-gray-400 hover:text-gray-600\"\n              >\n                <XMarkIcon className=\"h-6 w-6\" />\n              </button>\n            </div>\n            \n            <nav className=\"flex-1 px-6 py-6\">\n              <ul className=\"space-y-2\">\n                {navigation.map((item) => (\n                  <li key={item.name}>\n                    <Link\n                      href={item.href}\n                      className={cn(\n                        'flex items-center gap-3 px-3 py-2 rounded-md text-sm font-medium transition-colors',\n                        pathname === item.href\n                          ? 'bg-primary-100 text-primary-700'\n                          : 'text-gray-700 hover:bg-gray-100'\n                      )}\n                      onClick={() => setSidebarOpen(false)}\n                    >\n                      <item.icon className=\"h-5 w-5\" />\n                      {item.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </nav>\n\n            <div className=\"border-t border-gray-200 p-6\">\n              <div className=\"flex items-center gap-3 mb-4\">\n                <div className=\"h-8 w-8 bg-primary-100 rounded-full flex items-center justify-center\">\n                  <span className=\"text-sm font-medium text-primary-700\">\n                    {profile?.full_name?.charAt(0) || user?.email?.charAt(0) || 'U'}\n                  </span>\n                </div>\n                <div className=\"flex-1 min-w-0\">\n                  <p className=\"text-sm font-medium text-gray-900 truncate\">\n                    {profile?.full_name || user?.email}\n                  </p>\n                  {profile?.area && (\n                    <p className=\"text-xs text-gray-500 capitalize\">{profile.area}</p>\n                  )}\n                </div>\n              </div>\n              <button\n                onClick={handleSignOut}\n                className=\"flex items-center gap-2 text-sm text-gray-700 hover:text-gray-900 transition-colors\"\n              >\n                <ArrowRightOnRectangleIcon className=\"h-4 w-4\" />\n                Sair\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col\">\n        <div className=\"flex flex-col flex-1 bg-white border-r border-gray-200\">\n          <div className=\"flex h-16 items-center px-6 border-b border-gray-200\">\n            <Link href=\"/dashboard\" className=\"text-xl font-bold text-primary-600\">\n              AgentePub\n            </Link>\n          </div>\n          \n          <nav className=\"flex-1 px-6 py-6\">\n            <ul className=\"space-y-2\">\n              {navigation.map((item) => (\n                <li key={item.name}>\n                  <Link\n                    href={item.href}\n                    className={cn(\n                      'flex items-center gap-3 px-3 py-2 rounded-md text-sm font-medium transition-colors',\n                      pathname === item.href\n                        ? 'bg-primary-100 text-primary-700'\n                        : 'text-gray-700 hover:bg-gray-100'\n                    )}\n                  >\n                    <item.icon className=\"h-5 w-5\" />\n                    {item.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </nav>\n\n          <div className=\"border-t border-gray-200 p-6\">\n            <div className=\"flex items-center gap-3 mb-4\">\n              <div className=\"h-8 w-8 bg-primary-100 rounded-full flex items-center justify-center\">\n                <span className=\"text-sm font-medium text-primary-700\">\n                  {profile?.full_name?.charAt(0) || user?.email?.charAt(0) || 'U'}\n                </span>\n              </div>\n              <div className=\"flex-1 min-w-0\">\n                <p className=\"text-sm font-medium text-gray-900 truncate\">\n                  {profile?.full_name || user?.email}\n                </p>\n                {profile?.area && (\n                  <p className=\"text-xs text-gray-500 capitalize\">{profile.area}</p>\n                )}\n              </div>\n            </div>\n            <button\n              onClick={handleSignOut}\n              className=\"flex items-center gap-2 text-sm text-gray-700 hover:text-gray-900 transition-colors\"\n            >\n              <ArrowRightOnRectangleIcon className=\"h-4 w-4\" />\n              Sair\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"lg:pl-64\">\n        {/* Top bar */}\n        <div className=\"sticky top-0 z-40 bg-white border-b border-gray-200 px-6 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <button\n              onClick={() => setSidebarOpen(true)}\n              className=\"lg:hidden text-gray-500 hover:text-gray-700\"\n            >\n              <Bars3Icon className=\"h-6 w-6\" />\n            </button>\n            \n            <div className=\"flex items-center gap-4\">\n              <div className=\"text-sm text-gray-500\">\n                {new Date().toLocaleDateString('pt-BR', { \n                  weekday: 'long', \n                  year: 'numeric', \n                  month: 'long', \n                  day: 'numeric' \n                })}\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"px-6 py-8\">\n          {children}\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAPA;;;;;;;;AAmBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,2NAAQ;IAAC;IACxD;QAAE,MAAM;QAAa,MAAM;QAAwB,MAAM,iOAAU;IAAC;IACpE;QAAE,MAAM;QAAW,MAAM;QAAsB,MAAM,0OAAa;IAAC;IACnE;QAAE,MAAM;QAAc,MAAM;QAAyB,MAAM,+PAAoB;IAAC;IAChF;QAAE,MAAM;QAAQ,MAAM;QAAmB,MAAM,wQAAuB;IAAC;IACvE;QAAE,MAAM;QAAiB,MAAM;QAA4B,MAAM,0OAAa;IAAC;CAChF;AAMc,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;IACxE,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,iNAAQ,EAAC;IAC/C,MAAM,WAAW,IAAA,iJAAW;IAC5B,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,IAAA,0IAAO;IAE1C,MAAM,gBAAgB;QACpB,MAAM;IACR;IAEA,qBACE,8OAAC;QAAI,WAAU;;YAEZ,6BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;wBAA0C,SAAS,IAAM,eAAe;;;;;;kCACvF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,uKAAI;wCAAC,MAAK;wCAAa,WAAU;kDAAqC;;;;;;kDAGvE,8OAAC;wCACC,SAAS,IAAM,eAAe;wCAC9B,WAAU;kDAEV,cAAA,8OAAC,8NAAS;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAIzB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CACX,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;sDACC,cAAA,8OAAC,uKAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAW,IAAA,yHAAE,EACX,sFACA,aAAa,KAAK,IAAI,GAClB,oCACA;gDAEN,SAAS,IAAM,eAAe;;kEAE9B,8OAAC,KAAK,IAAI;wDAAC,WAAU;;;;;;oDACpB,KAAK,IAAI;;;;;;;2CAZL,KAAK,IAAI;;;;;;;;;;;;;;;0CAmBxB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DACb,SAAS,WAAW,OAAO,MAAM,MAAM,OAAO,OAAO,MAAM;;;;;;;;;;;0DAGhE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEACV,SAAS,aAAa,MAAM;;;;;;oDAE9B,SAAS,sBACR,8OAAC;wDAAE,WAAU;kEAAoC,QAAQ,IAAI;;;;;;;;;;;;;;;;;;kDAInE,8OAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,8OAAC,8QAAyB;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;;;;;;;;;;;;;0BAS3D,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,uKAAI;gCAAC,MAAK;gCAAa,WAAU;0CAAqC;;;;;;;;;;;sCAKzE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;0CACX,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;kDACC,cAAA,8OAAC,uKAAI;4CACH,MAAM,KAAK,IAAI;4CACf,WAAW,IAAA,yHAAE,EACX,sFACA,aAAa,KAAK,IAAI,GAClB,oCACA;;8DAGN,8OAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;gDACpB,KAAK,IAAI;;;;;;;uCAXL,KAAK,IAAI;;;;;;;;;;;;;;;sCAkBxB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DACb,SAAS,WAAW,OAAO,MAAM,MAAM,OAAO,OAAO,MAAM;;;;;;;;;;;sDAGhE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DACV,SAAS,aAAa,MAAM;;;;;;gDAE9B,SAAS,sBACR,8OAAC;oDAAE,WAAU;8DAAoC,QAAQ,IAAI;;;;;;;;;;;;;;;;;;8CAInE,8OAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,8OAAC,8QAAyB;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;;;;;;;;;;;;;0BAQzD,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CAEV,cAAA,8OAAC,8NAAS;wCAAC,WAAU;;;;;;;;;;;8CAGvB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACZ,IAAI,OAAO,kBAAkB,CAAC,SAAS;4CACtC,SAAS;4CACT,MAAM;4CACN,OAAO;4CACP,KAAK;wCACP;;;;;;;;;;;;;;;;;;;;;;kCAOR,8OAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 636, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent3/agentepub/src/app/dashboard/agentes/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport ProtectedRoute from '@/components/auth/ProtectedRoute'\nimport DashboardLayout from '@/components/dashboard/DashboardLayout'\nimport {\n  UserGroupIcon,\n  CalendarDaysIcon,\n  ChartBarIcon,\n  SparklesIcon,\n  DocumentTextIcon,\n  ArrowRightIcon\n} from '@heroicons/react/24/outline'\n\nconst agents = [\n  {\n    id: 'atendimento',\n    name: 'Agente de Atendimento',\n    description: 'Organiza briefings, acompanha prazos e mantém a comunicação com clientes sempre em dia.',\n    icon: UserGroupIcon,\n    color: 'blue',\n    capabilities: [\n      'Estrutura briefings automaticamente',\n      'Cria cronogramas de aprovação',\n      'Monitora prazos e entregas',\n      'Gera relatórios de status'\n    ],\n    inputPlaceholder: 'Cole aqui o briefing do cliente ou descreva a demanda...',\n    examples: [\n      'Briefing: Campanha para lançamento de produto X, público jovem 18-25 anos, budget R$ 50k',\n      'Cliente solicitou alterações no cronograma da campanha Y',\n      'Preciso organizar as aprovações para a campanha de Black Friday'\n    ]\n  },\n  {\n    id: 'planejamento',\n    name: 'Agente de Planejamento',\n    description: 'Gera cronogramas inteligentes, insights estratégicos e organiza toda a execução da campanha.',\n    icon: CalendarDaysIcon,\n    color: 'green',\n    capabilities: [\n      'Cronogramas automáticos',\n      'Análise de concorrência',\n      'Sugestões de estratégia',\n      'Distribuição de budget'\n    ],\n    inputPlaceholder: 'Descreva o projeto ou campanha que precisa ser planejada...',\n    examples: [\n      'Campanha de 60 dias para lançamento de app, budget R$ 100k, 3 fases',\n      'Planejamento estratégico para Black Friday, múltiplos canais',\n      'Cronograma para campanha institucional com prazo apertado'\n    ]\n  },\n  {\n    id: 'midia',\n    name: 'Agente de Mídia',\n    description: 'Analisa performance, sugere otimizações e gera relatórios com insights acionáveis.',\n    icon: ChartBarIcon,\n    color: 'purple',\n    capabilities: [\n      'Análise de KPIs',\n      'Sugestões de otimização',\n      'Relatórios automatizados',\n      'Alertas de performance'\n    ],\n    inputPlaceholder: 'Cole dados de performance ou descreva a campanha para análise...',\n    examples: [\n      'CTR: 2.1%, CPC: R$ 0.85, Budget gasto: R$ 15k de R$ 30k',\n      'Campanha Instagram com baixo engajamento, preciso otimizar',\n      'Análise de performance da campanha de verão nos últimos 30 dias'\n    ]\n  }\n]\n\nexport default function AgentesPage() {\n  const [selectedAgent, setSelectedAgent] = useState<string | null>(null)\n  const [input, setInput] = useState('')\n  const [isProcessing, setIsProcessing] = useState(false)\n  const [result, setResult] = useState<string | null>(null)\n\n  const currentAgent = agents.find(agent => agent.id === selectedAgent)\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    if (!input.trim() || !currentAgent) return\n\n    setIsProcessing(true)\n\n    // Simulate processing time\n    await new Promise(resolve => setTimeout(resolve, 2000))\n\n    // Generate simulated result based on agent type\n    const simulatedResult = generateAgentResult(currentAgent.id, input)\n    setResult(simulatedResult)\n    setIsProcessing(false)\n  }\n\n  const generateAgentResult = (agentId: string, inputText: string) => {\n    switch (agentId) {\n      case 'atendimento':\n        return `## 📋 Briefing Estruturado\n\n**Status:** Processado com sucesso\n**Data:** ${new Date().toLocaleDateString('pt-BR')}\n\n### Informações Principais\n• **Objetivo:** Identificado no briefing\n• **Público-alvo:** Definido conforme descrição\n• **Prazo:** Cronograma sugerido de 30 dias\n• **Budget:** Distribuição recomendada\n\n### Próximas Ações\n1. ✅ Validar informações com cliente\n2. ⏳ Definir cronograma detalhado\n3. ⏳ Alinhar expectativas de entrega\n4. ⏳ Iniciar produção de materiais\n\n### Alertas\n• Confirmar budget final\n• Definir pontos de aprovação\n• Estabelecer canais de comunicação\n\n**Recomendação:** Agendar reunião de alinhamento em 24h`\n\n      case 'planejamento':\n        return `## 📅 Cronograma Estratégico\n\n**Projeto:** Baseado no input fornecido\n**Duração:** 4 semanas\n**Fases:** 3 principais\n\n### Fase 1: Planejamento (Semana 1)\n• Definição de personas\n• Estratégia de conteúdo\n• Aprovação de conceitos\n• Setup de campanhas\n\n### Fase 2: Execução (Semanas 2-3)\n• Produção de materiais\n• Configuração de mídia\n• Testes A/B iniciais\n• Lançamento gradual\n\n### Fase 3: Otimização (Semana 4)\n• Análise de performance\n• Ajustes de targeting\n• Relatórios finais\n• Planejamento de continuidade\n\n### Distribuição de Budget\n• Mídia paga: 60%\n• Produção: 25%\n• Ferramentas: 10%\n• Contingência: 5%\n\n### KPIs Sugeridos\n• Alcance: 50k+ pessoas\n• Engajamento: 2.5%+\n• Conversões: 150+ leads`\n\n      case 'midia':\n        return `## 📊 Análise de Performance\n\n**Período:** Últimos 30 dias\n**Status:** Análise concluída\n\n### Métricas Principais\n• **Impressões:** 125.4k (+15% vs período anterior)\n• **Cliques:** 2.8k (CTR: 2.23%)\n• **CPC Médio:** R$ 0.92\n• **Conversões:** 89 (Taxa: 3.18%)\n\n### Insights Identificados\n🔍 **Oportunidades:**\n• Horários 18h-21h têm melhor performance\n• Público feminino 25-34 anos converte mais\n• Posts com vídeo têm 40% mais engajamento\n\n⚠️ **Pontos de Atenção:**\n• CPC aumentou 12% na última semana\n• Taxa de rejeição alta em mobile (65%)\n• Saturação do público em SP\n\n### Recomendações\n1. **Otimização de Horários:** Concentrar budget 18h-21h\n2. **Segmentação:** Focar em mulheres 25-34 anos\n3. **Criativos:** Priorizar conteúdo em vídeo\n4. **Expansão:** Testar novos mercados (RJ, BH)\n\n### Próximos Passos\n• Implementar otimizações sugeridas\n• Criar novos criativos em vídeo\n• Expandir targeting geográfico\n• Monitorar performance semanal`\n\n      default:\n        return 'Resultado não disponível para este agente.'\n    }\n  }\n\n  const resetAgent = () => {\n    setSelectedAgent(null)\n    setInput('')\n    setResult(null)\n  }\n\n  if (!selectedAgent) {\n    return (\n      <ProtectedRoute>\n        <DashboardLayout>\n          <div className=\"space-y-6\">\n            <div>\n              <h1 className=\"text-2xl font-bold text-gray-900\">Agentes Inteligentes</h1>\n              <p className=\"text-gray-600\">Escolha um agente especializado para ajudar com sua tarefa</p>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n              {agents.map((agent) => (\n                <div key={agent.id} className=\"bg-white rounded-lg shadow hover:shadow-md transition-shadow cursor-pointer\" onClick={() => setSelectedAgent(agent.id)}>\n                  <div className=\"p-6\">\n                    <div className=\"flex items-center gap-3 mb-4\">\n                      <div className={\n                        agent.id === 'atendimento' ? 'bg-blue-100 p-3 rounded-lg' :\n                          agent.id === 'planejamento' ? 'bg-green-100 p-3 rounded-lg' :\n                            'bg-purple-100 p-3 rounded-lg'\n                      }>\n                        <agent.icon className={\n                          agent.id === 'atendimento' ? 'h-6 w-6 text-blue-600' :\n                            agent.id === 'planejamento' ? 'h-6 w-6 text-green-600' :\n                              'h-6 w-6 text-purple-600'\n                        } />\n                      </div>\n                      <h3 className=\"font-semibold text-gray-900\">{agent.name}</h3>\n                    </div>\n\n                    <p className=\"text-gray-600 mb-4\">{agent.description}</p>\n\n                    <div className=\"space-y-2 mb-6\">\n                      {agent.capabilities.map((capability, idx) => (\n                        <div key={idx} className=\"flex items-center gap-2\">\n                          <div className=\"w-1.5 h-1.5 bg-primary-500 rounded-full\"></div>\n                          <span className=\"text-sm text-gray-700\">{capability}</span>\n                        </div>\n                      ))}\n                    </div>\n\n                    <button className=\"w-full bg-blue-600 text-white py-3 px-4 rounded-md font-semibold hover:bg-blue-700 transition-colors flex items-center justify-center gap-2\">\n                      🚀 Usar Agente\n                      <ArrowRightIcon className=\"h-5 w-5\" />\n                    </button>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </DashboardLayout>\n      </ProtectedRoute>\n    )\n  }\n\n  return (\n    <ProtectedRoute>\n      <DashboardLayout>\n        <div className=\"space-y-6\">\n          {/* Header */}\n          <div className=\"flex items-center gap-4\">\n            <button\n              onClick={resetAgent}\n              className=\"text-gray-500 hover:text-gray-700\"\n            >\n              ← Voltar\n            </button>\n            <div className=\"flex items-center gap-3\">\n              <div className={\n                currentAgent?.id === 'atendimento' ? 'bg-blue-100 p-2 rounded-lg' :\n                  currentAgent?.id === 'planejamento' ? 'bg-green-100 p-2 rounded-lg' :\n                    'bg-purple-100 p-2 rounded-lg'\n              }>\n                {currentAgent?.icon && <currentAgent.icon className={\n                  currentAgent?.id === 'atendimento' ? 'h-5 w-5 text-blue-600' :\n                    currentAgent?.id === 'planejamento' ? 'h-5 w-5 text-green-600' :\n                      'h-5 w-5 text-purple-600'\n                } />}\n              </div>\n              <div>\n                <h1 className=\"text-xl font-bold text-gray-900\">{currentAgent?.name}</h1>\n                <p className=\"text-sm text-gray-600\">{currentAgent?.description}</p>\n              </div>\n            </div>\n          </div>\n\n          {!result ? (\n            <div className=\"bg-white rounded-lg shadow p-6\">\n              <form onSubmit={handleSubmit} className=\"space-y-6\">\n                <div>\n                  <label htmlFor=\"input\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Descreva sua demanda\n                  </label>\n                  <textarea\n                    id=\"input\"\n                    value={input}\n                    onChange={(e) => setInput(e.target.value)}\n                    rows={6}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none\"\n                    placeholder={currentAgent?.inputPlaceholder}\n                    required\n                  />\n                </div>\n\n                <button\n                  type=\"submit\"\n                  disabled={isProcessing || !input.trim()}\n                  className=\"w-full bg-blue-600 text-white py-4 px-6 rounded-md font-semibold text-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all flex items-center justify-center gap-2 border-2 border-blue-600\"\n                  style={{ minHeight: '56px' }}\n                >\n                  {isProcessing ? (\n                    <>\n                      <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-white\"></div>\n                      ⏳ Processando...\n                    </>\n                  ) : (\n                    <>\n                      <SparklesIcon className=\"h-6 w-6\" />\n                      🚀 Processar com {currentAgent?.name}\n                    </>\n                  )}\n                </button>\n              </form>\n\n              {/* Examples */}\n              <div className=\"mt-8 pt-6 border-t border-gray-200\">\n                <h3 className=\"text-sm font-medium text-gray-900 mb-3\">💡 Exemplos:</h3>\n                <div className=\"space-y-2\">\n                  {currentAgent?.examples.map((example, idx) => (\n                    <button\n                      key={idx}\n                      onClick={() => setInput(example)}\n                      className=\"text-left text-sm text-blue-600 hover:text-blue-700 block w-full p-2 rounded hover:bg-blue-50 transition-colors\"\n                    >\n                      💡 {example}\n                    </button>\n                  ))}\n                </div>\n              </div>\n            </div>\n          ) : (\n            <div className=\"space-y-6\">\n              <div className=\"bg-white rounded-lg shadow p-6\">\n                <div className=\"flex items-center gap-2 mb-4\">\n                  <DocumentTextIcon className=\"h-5 w-5 text-green-500\" />\n                  <h2 className=\"text-lg font-semibold text-gray-900\">Resultado Processado</h2>\n                </div>\n\n                <div className=\"prose max-w-none\">\n                  <pre className=\"whitespace-pre-wrap text-sm text-gray-700 font-sans leading-relaxed\">\n                    {result}\n                  </pre>\n                </div>\n              </div>\n\n              <div className=\"flex gap-4\">\n                <button\n                  onClick={() => {\n                    setResult(null)\n                    setInput('')\n                  }}\n                  className=\"flex-1 bg-white text-gray-700 py-2 px-4 rounded-md font-medium border border-gray-300 hover:bg-gray-50 transition-colors\"\n                >\n                  Nova Consulta\n                </button>\n                <button className=\"flex-1 bg-green-600 text-white py-2 px-4 rounded-md font-medium hover:bg-green-700 transition-colors\">\n                  💾 Salvar em Campanha\n                </button>\n              </div>\n            </div>\n          )}\n        </div>\n      </DashboardLayout>\n    </ProtectedRoute>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAcA,MAAM,SAAS;IACb;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM,0OAAa;QACnB,OAAO;QACP,cAAc;YACZ;YACA;YACA;YACA;SACD;QACD,kBAAkB;QAClB,UAAU;YACR;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM,mPAAgB;QACtB,OAAO;QACP,cAAc;YACZ;YACA;YACA;YACA;SACD;QACD,kBAAkB;QAClB,UAAU;YACR;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM,uOAAY;QAClB,OAAO;QACP,cAAc;YACZ;YACA;YACA;YACA;SACD;QACD,kBAAkB;QAClB,UAAU;YACR;YACA;YACA;SACD;IACH;CACD;AAEc,SAAS;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,iNAAQ,EAAgB;IAClE,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,iNAAQ,EAAC;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,iNAAQ,EAAC;IACjD,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,iNAAQ,EAAgB;IAEpD,MAAM,eAAe,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IAEvD,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,cAAc;QAEpC,gBAAgB;QAEhB,2BAA2B;QAC3B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,gDAAgD;QAChD,MAAM,kBAAkB,oBAAoB,aAAa,EAAE,EAAE;QAC7D,UAAU;QACV,gBAAgB;IAClB;IAEA,MAAM,sBAAsB,CAAC,SAAiB;QAC5C,OAAQ;YACN,KAAK;gBACH,OAAO,CAAC;;;UAGN,EAAE,IAAI,OAAO,kBAAkB,CAAC,SAAS;;;;;;;;;;;;;;;;;;;uDAmBI,CAAC;YAElD,KAAK;gBACH,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAiCQ,CAAC;YAEnB,KAAK;gBACH,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+BAgCe,CAAC;YAE1B;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa;QACjB,iBAAiB;QACjB,SAAS;QACT,UAAU;IACZ;IAEA,IAAI,CAAC,eAAe;QAClB,qBACE,8OAAC,uJAAc;sBACb,cAAA,8OAAC,6JAAe;0BACd,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;sCAG/B,8OAAC;4BAAI,WAAU;sCACZ,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC;oCAAmB,WAAU;oCAA8E,SAAS,IAAM,iBAAiB,MAAM,EAAE;8CAClJ,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WACH,MAAM,EAAE,KAAK,gBAAgB,+BAC3B,MAAM,EAAE,KAAK,iBAAiB,gCAC5B;kEAEJ,cAAA,8OAAC,MAAM,IAAI;4DAAC,WACV,MAAM,EAAE,KAAK,gBAAgB,0BAC3B,MAAM,EAAE,KAAK,iBAAiB,2BAC5B;;;;;;;;;;;kEAGR,8OAAC;wDAAG,WAAU;kEAA+B,MAAM,IAAI;;;;;;;;;;;;0DAGzD,8OAAC;gDAAE,WAAU;0DAAsB,MAAM,WAAW;;;;;;0DAEpD,8OAAC;gDAAI,WAAU;0DACZ,MAAM,YAAY,CAAC,GAAG,CAAC,CAAC,YAAY,oBACnC,8OAAC;wDAAc,WAAU;;0EACvB,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAK,WAAU;0EAAyB;;;;;;;uDAFjC;;;;;;;;;;0DAOd,8OAAC;gDAAO,WAAU;;oDAA8I;kEAE9J,8OAAC,6OAAc;wDAAC,WAAU;;;;;;;;;;;;;;;;;;mCA9BtB,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;IAwChC;IAEA,qBACE,8OAAC,uJAAc;kBACb,cAAA,8OAAC,6JAAe;sBACd,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WACH,cAAc,OAAO,gBAAgB,+BACnC,cAAc,OAAO,iBAAiB,gCACpC;kDAEH,cAAc,sBAAQ,8OAAC,aAAa,IAAI;4CAAC,WACxC,cAAc,OAAO,gBAAgB,0BACnC,cAAc,OAAO,iBAAiB,2BACpC;;;;;;;;;;;kDAGR,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAmC,cAAc;;;;;;0DAC/D,8OAAC;gDAAE,WAAU;0DAAyB,cAAc;;;;;;;;;;;;;;;;;;;;;;;;oBAKzD,CAAC,uBACA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,UAAU;gCAAc,WAAU;;kDACtC,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAQ,WAAU;0DAA+C;;;;;;0DAGhF,8OAAC;gDACC,IAAG;gDACH,OAAO;gDACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gDACxC,MAAM;gDACN,WAAU;gDACV,aAAa,cAAc;gDAC3B,QAAQ;;;;;;;;;;;;kDAIZ,8OAAC;wCACC,MAAK;wCACL,UAAU,gBAAgB,CAAC,MAAM,IAAI;wCACrC,WAAU;wCACV,OAAO;4CAAE,WAAW;wCAAO;kDAE1B,6BACC;;8DACE,8OAAC;oDAAI,WAAU;;;;;;gDAAkE;;yEAInF;;8DACE,8OAAC,uOAAY;oDAAC,WAAU;;;;;;gDAAY;gDAClB,cAAc;;;;;;;;;;;;;;0CAOxC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,8OAAC;wCAAI,WAAU;kDACZ,cAAc,SAAS,IAAI,CAAC,SAAS,oBACpC,8OAAC;gDAEC,SAAS,IAAM,SAAS;gDACxB,WAAU;;oDACX;oDACK;;+CAJC;;;;;;;;;;;;;;;;;;;;;6CAWf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,mPAAgB;gDAAC,WAAU;;;;;;0DAC5B,8OAAC;gDAAG,WAAU;0DAAsC;;;;;;;;;;;;kDAGtD,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACZ;;;;;;;;;;;;;;;;;0CAKP,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS;4CACP,UAAU;4CACV,SAAS;wCACX;wCACA,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCAAO,WAAU;kDAAuG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUzI", "debugId": null}}]}