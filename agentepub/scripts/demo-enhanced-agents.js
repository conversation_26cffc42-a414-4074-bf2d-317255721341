#!/usr/bin/env node

/**
 * Enhanced Agent System Demonstration
 * 
 * This script demonstrates the capabilities of the enhanced agent system
 * by processing various types of inputs and showing the improved responses.
 */

const { generateAgentResponse, analyzeUserInput } = require('../src/lib/agents/enhancedAgentSystem')

// Demo inputs for different scenarios
const demoInputs = {
  atendimento: [
    {
      name: "Briefing Básico",
      input: "Cliente quer campanha para Black Friday"
    },
    {
      name: "Briefing Completo",
      input: "Cliente TechCorp precisa de campanha de awareness para lançamento de produto B2B, público-alvo CTOs e desenvolvedores, budget R$ 150.000, prazo 60 dias, canais LinkedIn e Google Ads, objetivo 300 leads qualificados"
    },
    {
      name: "Situação Urgente",
      input: "URGENTE: Cliente aprovou campanha mas precisa começar hoje, budget R$ 80k, produto de e-commerce, público jovem 18-25 anos"
    }
  ],
  planejamento: [
    {
      name: "Planejamento Estratégico",
      input: "Startup de fintech precisa de estratégia de marketing para crescer de 1k para 10k usuários em 6 meses, budget R$ 200k"
    },
    {
      name: "Campanha Multi-canal",
      input: "Planejar campanha integrada para marca de moda, usando Instagram, TikTok e influenciadores, público feminino 20-35 anos, lançamento de coleção verão"
    }
  ],
  midia: [
    {
      name: "Análise de Performance",
      input: "Campanha atual no Facebook Ads: CPC R$ 2,50, CTR 1.8%, 50k impressões, 15 conversões. Como otimizar?"
    },
    {
      name: "Otimização de Budget",
      input: "Budget de R$ 100k/mês distribuído: Google Ads 60%, Facebook 30%, LinkedIn 10%. Performance Google: CPA R$ 150, Facebook: CPA R$ 200, LinkedIn: CPA R$ 300"
    }
  ]
}

async function runDemo() {
  console.log('🚀 Enhanced Agent System Demonstration\n')
  console.log('=' .repeat(60))
  
  for (const [agentId, inputs] of Object.entries(demoInputs)) {
    console.log(`\n📋 AGENT: ${agentId.toUpperCase()}`)
    console.log('-'.repeat(40))
    
    for (const demo of inputs) {
      console.log(`\n💡 Scenario: ${demo.name}`)
      console.log(`📝 Input: "${demo.input}"`)
      console.log('\n🔍 Analysis:')
      
      try {
        // First, show input analysis
        const analysis = analyzeUserInput(demo.input)
        console.log(`   • Complexity: ${analysis.complexity}`)
        console.log(`   • Urgency: ${analysis.urgency}`)
        console.log(`   • Completeness: ${Math.round(analysis.completeness * 100)}%`)
        console.log(`   • Sentiment: ${analysis.sentiment}`)
        console.log(`   • Keywords: ${analysis.keywords.slice(0, 5).join(', ')}`)
        
        if (analysis.riskFactors.length > 0) {
          console.log(`   • Risk Factors: ${analysis.riskFactors.length} identified`)
        }
        
        if (analysis.opportunityIndicators.length > 0) {
          console.log(`   • Opportunities: ${analysis.opportunityIndicators.length} identified`)
        }
        
        // Generate enhanced response
        console.log('\n⚡ Generating enhanced response...')
        const startTime = Date.now()
        const response = await generateAgentResponse(agentId, demo.input)
        const processingTime = Date.now() - startTime
        
        console.log('\n📊 Quality Metrics:')
        console.log(`   • Confidence: ${Math.round(response.metadata.confidence * 100)}%`)
        console.log(`   • Completeness: ${Math.round(response.metadata.completeness * 100)}%`)
        console.log(`   • Quality Score: ${Math.round(response.metadata.qualityScore * 100)}%`)
        console.log(`   • Processing Time: ${processingTime}ms`)
        console.log(`   • Sections Generated: ${response.response.sections.length}`)
        console.log(`   • Action Items: ${response.response.actionItems.length}`)
        console.log(`   • Follow-up Questions: ${response.response.followUpQuestions.length}`)
        
        if (response.metadata.followUpRecommended) {
          console.log('   • 💡 Follow-up conversation recommended')
        }
        
        // Show response preview (first 200 characters)
        console.log('\n📄 Response Preview:')
        const preview = response.formattedOutput.substring(0, 200).replace(/\n/g, ' ')
        console.log(`   "${preview}..."`)
        
        // Show action items if any
        if (response.response.actionItems.length > 0) {
          console.log('\n✅ Key Action Items:')
          response.response.actionItems.slice(0, 3).forEach((item, index) => {
            console.log(`   ${index + 1}. ${item}`)
          })
        }
        
        console.log('\n' + '─'.repeat(60))
        
      } catch (error) {
        console.error(`❌ Error processing demo: ${error.message}`)
      }
    }
  }
  
  // Performance comparison
  console.log('\n🏆 PERFORMANCE COMPARISON')
  console.log('=' .repeat(60))
  
  const testInput = "Cliente precisa de campanha para produto novo com budget R$ 50k"
  
  console.log('\n📊 Processing Multiple Agents:')
  const agents = ['atendimento', 'planejamento', 'midia']
  
  for (const agentId of agents) {
    const startTime = Date.now()
    try {
      const response = await generateAgentResponse(agentId, testInput)
      const processingTime = Date.now() - startTime
      
      console.log(`   ${agentId}: ${processingTime}ms | Quality: ${Math.round(response.metadata.qualityScore * 100)}% | Sections: ${response.response.sections.length}`)
    } catch (error) {
      console.log(`   ${agentId}: ERROR - ${error.message}`)
    }
  }
  
  // Batch processing demo
  console.log('\n🔄 Batch Processing Demo:')
  const batchInputs = [
    "Briefing para campanha A",
    "Análise de performance B", 
    "Planejamento estratégico C"
  ]
  
  const startTime = Date.now()
  try {
    const { enhancedAgentSystem } = require('../src/lib/agents/enhancedAgentSystem')
    const batchResponses = await enhancedAgentSystem.generateBatchResponses('atendimento', batchInputs)
    const totalTime = Date.now() - startTime
    
    console.log(`   Processed ${batchResponses.length} inputs in ${totalTime}ms`)
    console.log(`   Average time per input: ${Math.round(totalTime / batchResponses.length)}ms`)
    
    const avgQuality = batchResponses.reduce((sum, r) => sum + r.metadata.qualityScore, 0) / batchResponses.length
    console.log(`   Average quality score: ${Math.round(avgQuality * 100)}%`)
    
  } catch (error) {
    console.error(`   Batch processing error: ${error.message}`)
  }
  
  console.log('\n✨ Demo completed! The enhanced agent system provides:')
  console.log('   • Deep input analysis and context understanding')
  console.log('   • Comprehensive, structured responses')
  console.log('   • Quality metrics and validation')
  console.log('   • Agent-specific expertise and recommendations')
  console.log('   • Actionable insights and next steps')
  console.log('   • Performance monitoring and optimization')
  
  console.log('\n🔗 Next Steps:')
  console.log('   • Run tests: npm run test:agents')
  console.log('   • Check documentation: src/lib/agents/README.md')
  console.log('   • Try the enhanced UI in the dashboard')
}

// Handle command line execution
if (require.main === module) {
  runDemo().catch(error => {
    console.error('Demo failed:', error)
    process.exit(1)
  })
}

module.exports = { runDemo }
